
import requests
import json
import logging
from fastmcp import FastMCP
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 创建MCP服务
app = FastMCP(name="查询地区")

@app.tool()
def api_query(areaLevel: str = "3", parentAreaId: str = "1431") -> Dict[str, Any]:
    """
     查询地区信息树
    
     Args:
         area_level: 地区级别（1=省，2=市，3=县，4=镇，默认为3）
        parent_area_id: 父级地区ID（1431为四川省ID，默认为1431）
    
     Returns:
         地区信息树结果
    """
    
    # API配置
    url = "https://************/gateway3/online-service/chatSessionRecord/getAreaTree"
    method = "POST"
    headers = {'Content-Type': 'application/json'}
    base_params = {'areaLevel': '3', 'parentAreaId': '1431'}
    timeout = 30
    
    try:
        # 智能合并基础参数和传入参数
        request_params = base_params.copy()

        # 构建传入参数字典
        input_params = {"areaLevel": areaLevel, "parentAreaId": parentAreaId}

        # 如果基础参数中有inputs字段，则将参数合并到inputs中
        if "inputs" in request_params and isinstance(request_params["inputs"], dict):
            request_params["inputs"].update(input_params)
        else:
            # 否则直接合并到顶层
            request_params.update(input_params)

        logger.info(f"调用API: {url}")
        logger.info(f"请求参数: {request_params}")
        
        # 根据HTTP方法发送请求
        if method.upper() == "GET":
            response = requests.get(
                url, 
                params=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False  # 注意：生产环境中应该验证SSL证书
            )
        elif method.upper() == "POST":
            response = requests.post(
                url, 
                json=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False
            )
        elif method.upper() == "PUT":
            response = requests.put(
                url, 
                json=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False
            )
        elif method.upper() == "DELETE":
            response = requests.delete(
                url, 
                json=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False
            )
        else:
            return {"error": f"不支持的HTTP方法: {method}"}
        
        # 检查响应状态
        if response.status_code == 200:
            try:
                result = response.json()
                logger.info("API调用成功")
                return result
            except json.JSONDecodeError:
                # 如果响应不是JSON格式，返回文本内容
                return {"content": response.text, "content_type": "text"}
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            return {
                "error": f"API请求失败，状态码: {response.status_code}",
                "status_code": response.status_code,
                "response": response.text
            }
    
    except requests.exceptions.Timeout:
        logger.error("API请求超时")
        return {"error": "API请求超时"}
    except requests.exceptions.ConnectionError:
        logger.error("API连接失败")
        return {"error": "API连接失败"}
    except Exception as e:
        logger.error(f"请求过程中发生错误: {str(e)}")
        return {"error": f"请求过程中发生错误: {str(e)}"}

@app.tool()
def get_service_info() -> Dict[str, Any]:
    """获取服务信息"""
    return {
        "service_id": "2699649c-0fe2-4d08-830e-063a2b2606c0",
        "name": "查询地区",
        "description": "查询地区树",
        "api_url": "https://************/gateway3/online-service/chatSessionRecord/getAreaTree",
        "method": "POST",
        "port": 15238,
        "status": "running"
    }

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="查询地区")
    parser.add_argument("--port", type=int, default=15238, help="服务端口")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")

    args = parser.parse_args()

    print(f"启动MCP服务: 查询地区")
    print(f"地址: http://{args.host}:{args.port}")
    print(f"MCP端点: http://{args.host}:{args.port}/mcp")
    print(f"API URL: https://************/gateway3/online-service/chatSessionRecord/getAreaTree")

    # 使用streamable-http传输，这是MCP客户端期望的协议
    app.run(transport="http", host=args.host, port=args.port)
