#!/usr/bin/env python3
"""
MCP服务管理器API测试脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    response = requests.get(f"{BASE_URL}/api/health")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_create_service():
    """测试创建服务"""
    print("🚀 测试创建服务...")
    
    service_data = {
        "name": "测试API服务",
        "description": "这是一个测试用的API服务",
        "api_config": {
            "url": "https://httpbin.org/post",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json"
            },
            "params": {
                "test": "true"
            },
            "timeout": 30
        }
    }
    
    response = requests.post(f"{BASE_URL}/api/services", json=service_data)
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    if response.status_code == 201:
        return result.get("service_id")
    return None

def test_list_services():
    """测试获取服务列表"""
    print("📋 测试获取服务列表...")
    response = requests.get(f"{BASE_URL}/api/services")
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"服务数量: {result.get('total', 0)}")
    
    for service in result.get('services', []):
        print(f"  - {service['name']} (ID: {service['service_id'][:8]}..., 状态: {service['status']})")
    print()
    
    return result.get('services', [])

def test_get_service_detail(service_id):
    """测试获取服务详情"""
    print(f"🔍 测试获取服务详情 (ID: {service_id[:8]}...)...")
    response = requests.get(f"{BASE_URL}/api/services/{service_id}")
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        service = result['service']
        print(f"服务名称: {service['name']}")
        print(f"服务状态: {service['status']}")
        print(f"服务端口: {service['port']}")
        print(f"API URL: {service['api_config']['url']}")
    else:
        print(f"错误: {response.json()}")
    print()

def test_start_service(service_id):
    """测试启动服务"""
    print(f"▶️ 测试启动服务 (ID: {service_id[:8]}...)...")
    response = requests.post(f"{BASE_URL}/api/services/{service_id}/start")
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
    print()
    
    if result.get("success"):
        return result.get("port")
    return None

def test_stop_service(service_id):
    """测试停止服务"""
    print(f"⏹️ 测试停止服务 (ID: {service_id[:8]}...)...")
    response = requests.post(f"{BASE_URL}/api/services/{service_id}/stop")
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
    print()

def test_delete_service(service_id):
    """测试删除服务"""
    print(f"🗑️ 测试删除服务 (ID: {service_id[:8]}...)...")
    response = requests.delete(f"{BASE_URL}/api/services/{service_id}")
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
    print()

def test_mcp_service(port):
    """测试生成的MCP服务"""
    print(f"🔧 测试MCP服务 (端口: {port})...")
    
    try:
        # 等待服务启动
        time.sleep(2)
        
        # 测试服务是否可访问
        mcp_url = f"http://localhost:{port}"
        response = requests.get(f"{mcp_url}/health", timeout=5)
        print(f"MCP服务健康检查: {response.status_code}")
        
    except requests.exceptions.RequestException as e:
        print(f"MCP服务连接失败: {e}")
    print()

def main():
    """主测试函数"""
    print("🧪 MCP服务管理器API测试")
    print("=" * 50)
    
    try:
        # 1. 健康检查
        test_health()
        
        # 2. 创建服务
        service_id = test_create_service()
        if not service_id:
            print("❌ 创建服务失败，停止测试")
            return
        
        # 3. 获取服务列表
        services = test_list_services()
        
        # 4. 获取服务详情
        test_get_service_detail(service_id)
        
        # 5. 启动服务
        port = test_start_service(service_id)
        
        # 6. 测试MCP服务
        if port:
            test_mcp_service(port)
        
        # 7. 再次获取服务列表（查看状态变化）
        print("📋 启动后的服务列表:")
        test_list_services()
        
        # 8. 停止服务
        test_stop_service(service_id)
        
        # 9. 删除服务
        test_delete_service(service_id)
        
        # 10. 最终服务列表
        print("📋 清理后的服务列表:")
        test_list_services()
        
        print("✅ 所有测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到MCP服务管理器，请确保服务已启动 (http://localhost:8000)")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
