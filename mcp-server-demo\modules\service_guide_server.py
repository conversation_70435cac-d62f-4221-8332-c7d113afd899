from fastmcp import FastMCP
import requests
import logging
import json
import os
from typing import Dict, Any, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 地区编码查询类
class AreaCodeLookup:
    def __init__(self):
        """初始化地区编码查询工具"""
        self.data = None
        self.load_data()

    def load_data(self):
        """加载地区数据"""
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            data_file = os.path.join(current_dir, 'area_code_data.json')

            with open(data_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
                logging.info("地区编码数据加载成功")
        except FileNotFoundError:
            logging.error("地区数据文件未找到，请确保 area_code_data.json 文件存在")
            self.data = None
        except json.JSONDecodeError:
            logging.error("地区数据文件格式错误")
            self.data = None

    def search_by_name(self, area_name: str) -> List[Dict[str, Any]]:
        """根据地区名称搜索地区编码"""
        if not self.data:
            return []

        results = []

        # 搜索省级
        if area_name in self.data['province']['areaName']:
            results.append({
                'level': '省',
                'name': self.data['province']['areaName'],
                'code': self.data['province']['areaCode'],
                'id': self.data['province']['id']
            })

        # 搜索市级
        for city in self.data['cities']:
            if area_name in city['areaName'] or area_name in city['areaSimpleName']:
                results.append({
                    'level': '市',
                    'name': city['areaName'],
                    'simple_name': city['areaSimpleName'],
                    'code': city['areaCode'],
                    'id': city['id']
                })

            # 搜索区县级
            for district in city.get('districts', []):
                if area_name in district['areaName'] or area_name in district['areaSimpleName']:
                    results.append({
                        'level': '区县',
                        'name': district['areaName'],
                        'simple_name': district['areaSimpleName'],
                        'code': district['areaCode'],
                        'id': district['id'],
                        'parent_city': city['areaName']
                    })

        return results

    def get_code_by_name(self, area_name: str) -> Optional[str]:
        """根据地区名称获取地区编码（返回第一个匹配结果的编码）"""
        results = self.search_by_name(area_name)
        if results:
            return results[0]['code']
        return None

    def get_city_districts(self, city_name: str) -> Optional[Dict[str, Any]]:
        """获取指定城市的所有区县"""
        if not self.data:
            return None

        for city in self.data['cities']:
            if city_name in city['areaName'] or city_name in city['areaSimpleName']:
                return {
                    'city': city['areaName'],
                    'city_code': city['areaCode'],
                    'districts': city.get('districts', [])
                }
        return None

# 创建地区编码查询实例
area_lookup = AreaCodeLookup()

# 创建办事指南查询MCP服务
service_guide_mcp = FastMCP(name="ServiceGuideService")

@service_guide_mcp.tool()
def search_service_events(query: str, area_code: str = "5100000", top_k: int = 4, score_threshold: float = 1.2) -> Dict[str, Any]:
    """
    根据用户问题查询相关事项列表和ID
    
    Args:
        query: 根据用户问题提取的事项名称
        area_code: 地区编码（默认为四川省：5100000）
        top_k: 返回结果数量（默认为4）
        score_threshold: 分数阈值（默认为1.2）
    
    Returns:
        包含事项列表、事项数据和地区数据的结果
    """
    url = "http://10.206.23.123:9873/knowledge_base/search_event"
    
    # 设置请求参数
    payload = {
        "query": query,
        "top_k": top_k,
        "score_threshold": score_threshold,
        "area_code": area_code
    }
    
    # 设置请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            logging.info(f"成功查询到 {len(result.get('event_data_list', []))} 个相关事项")
            return result
        else:
            logging.error(f"事项搜索API请求失败，状态码: {response.status_code}")
            return {
                "code": "error", 
                "msg": f"事项搜索API请求失败，状态码: {response.status_code}",
                "event_name_list": [],
                "event_data_list": [],
                "area_data_list": []
            }
    
    except Exception as e:
        logging.error(f"事项搜索请求过程中发生错误: {str(e)}")
        return {
            "code": "error", 
            "msg": f"事项搜索请求过程中发生错误: {str(e)}",
            "event_name_list": [],
            "event_data_list": [],
            "area_data_list": []
        }

# @service_guide_mcp.tool()
# def get_area_tree(area_level: str = "3", parent_area_id: str = "1431") -> Dict[str, Any]:
#     """
#     查询地区信息树
    
#     Args:
#         area_level: 地区级别（1=省，2=市，3=县，4=镇，默认为3）
#         parent_area_id: 父级地区ID（1431为四川省ID，默认为1431）
    
#     Returns:
#         地区信息树结果
#     """
#     url = "https://10.206.23.71/gateway3/online-service/chatSessionRecord/getAreaTree"
    
#     # 设置请求参数
#     payload = {
#         "areaLevel": area_level,
#         "parentAreaId": parent_area_id
#     }
    
#     # 设置请求头
#     headers = {
#         "Content-Type": "application/json"
#     }
    
#     try:
#         # 注意：正式环境中应验证SSL证书，这里因为是内网地址，可能需要禁用证书验证
#         response = requests.post(url, headers=headers, json=payload, verify=False, timeout=30)
        
#         # 检查响应状态
#         if response.status_code == 200:
#             result = response.json()
#             logging.info(f"成功查询地区信息，级别: {area_level}, 父级ID: {parent_area_id}")
#             return result
#         else:
#             logging.error(f"地区查询API请求失败，状态码: {response.status_code}")
#             return {"code": "error", "msg": f"地区查询API请求失败，状态码: {response.status_code}"}
    
#     except Exception as e:
#         logging.error(f"地区查询请求过程中发生错误: {str(e)}")
#         return {"code": "error", "msg": f"地区查询请求过程中发生错误: {str(e)}"}

@service_guide_mcp.tool()
def search_event_detail(query: str, event_id: str, top_k: int = 4, data_type: str = "eventGuide", score_threshold: float = 0.85) -> Dict[str, Any]:
    """
    查询办事指南详情
    
    Args:
        query: 用户提问提取的问题（如"在哪里办理？"）
        event_id: 事项ID（从search_service_events接口返回）
        top_k: 返回结果数量（默认为4）
        data_type: 数据类型（固定值：eventGuide）
        score_threshold: 分数阈值（默认为0.85）
    
    Returns:
        办事指南详情信息
    """
    url = "http://10.206.23.123:9873/knowledge_base/search_event_key"
    
    # 设置请求参数
    payload = {
        "query": query,
        "event_id": event_id,
        "top_k": top_k,
        "dataType": data_type,
        "score_threshold": score_threshold
    }
    
    # 设置请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            logging.info(f"成功查询事项详情，事项ID: {event_id}")
            return result
        else:
            logging.error(f"事项详情查询API请求失败，状态码: {response.status_code}")
            return {"code": "error", "msg": f"事项详情查询API请求失败，状态码: {response.status_code}"}
    
    except Exception as e:
        logging.error(f"事项详情查询请求过程中发生错误: {str(e)}")
        return {"code": "error", "msg": f"事项详情查询请求过程中发生错误: {str(e)}"}


@service_guide_mcp.tool()
def search_area_code(area_name: str) -> Dict[str, Any]:
    """
    根据地区名称搜索地区编码（本地缓存查询，无需调用API）

    Args:
        area_name: 地区名称（如：成都市、高新区、武侯区等）

    Returns:
        包含匹配地区信息的结果
    """
    try:
        results = area_lookup.search_by_name(area_name)

        if results:
            return {
                "success": True,
                "area_name": area_name,
                "matches": results,
                "primary_code": results[0]['code'],  # 返回第一个匹配的编码作为主要编码
                "msg": f"找到 {len(results)} 个匹配的地区，主要编码: {results[0]['code']}"
            }
        else:
            return {
                "success": False,
                "area_name": area_name,
                "matches": [],
                "primary_code": None,
                "msg": f"未找到匹配的地区: {area_name}"
            }
    except Exception as e:
        logging.error(f"地区编码查询过程中发生错误: {str(e)}")
        return {
            "success": False,
            "area_name": area_name,
            "matches": [],
            "primary_code": None,
            "msg": f"地区编码查询过程中发生错误: {str(e)}"
        }