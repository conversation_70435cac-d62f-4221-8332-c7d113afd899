import asyncio
import logging
import signal
import sys
import os
from pathlib import Path
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.mcp_service_manager.service_manager import ServiceManager
from src.mcp_service_manager.api.routes import router, set_service_manager

# 确保日志目录存在
os.makedirs('logs', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/mcp_service_manager.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="MCP服务管理器",
    description="可配置化的MCP服务管理系统",
    version="0.1.0"
)

# 全局服务管理器
service_manager = None

def setup_static_files():
    """设置静态文件和模板"""
    # 获取当前文件的目录
    current_dir = Path(__file__).parent
    
    # 静态文件目录
    static_dir = current_dir / "web" / "static"
    if static_dir.exists():
        app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
        logger.info(f"静态文件目录: {static_dir}")
    
    # 模板目录
    template_dir = current_dir / "web" / "templates"
    if template_dir.exists():
        global templates
        templates = Jinja2Templates(directory=str(template_dir))
        logger.info(f"模板目录: {template_dir}")
    else:
        logger.error(f"模板目录不存在: {template_dir}")

def setup_service_manager():
    """初始化服务管理器"""
    global service_manager
    
    # 确保必要的目录存在
    os.makedirs("configs", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    service_manager = ServiceManager()
    set_service_manager(service_manager)
    logger.info("服务管理器初始化完成")

def setup_signal_handlers():
    """设置信号处理器"""
    def signal_handler(signum, frame):
        logger.info(f"接收到信号 {signum}，正在关闭...")
        if service_manager:
            service_manager.shutdown()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

# 设置路由
app.include_router(router)

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("MCP服务管理器启动中...")
    setup_static_files()
    setup_service_manager()
    setup_signal_handlers()
    logger.info("MCP服务管理器启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("MCP服务管理器关闭中...")
    if service_manager:
        service_manager.shutdown()
    logger.info("MCP服务管理器已关闭")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="MCP服务管理器")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务端口")
    parser.add_argument("--reload", action="store_true", help="开发模式，自动重载")
    parser.add_argument("--log-level", default="info", help="日志级别")
    
    args = parser.parse_args()
    
    logger.info(f"启动MCP服务管理器")
    logger.info(f"管理界面: http://{args.host}:{args.port}")
    logger.info(f"API文档: http://{args.host}:{args.port}/docs")
    
    try:
        uvicorn.run(
            "src.mcp_service_manager.main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level
        )
    except KeyboardInterrupt:
        logger.info("用户中断，正在关闭...")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
