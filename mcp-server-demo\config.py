"""
配置文件 - 管理各种API密钥和配置参数
"""
import os
from typing import Optional

class Config:
    """配置类，管理所有服务的配置参数"""
    
    # Google Custom Search API配置
    GOOGLE_API_KEY: Optional[str] = os.getenv("GOOGLE_API_KEY", "")
    SEARCH_ENGINE_ID: Optional[str] = os.getenv("SEARCH_ENGINE_ID", "")
    
    # 其他API配置可以在这里添加
    # 例如：
    # OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY", "")
    # BAIDU_API_KEY: Optional[str] = os.getenv("BAIDU_API_KEY", "")
    
    @classmethod
    def get_google_search_config(cls) -> dict:
        """获取Google搜索配置"""
        return {
            "api_key": cls.GOOGLE_API_KEY,
            "search_engine_id": cls.SEARCH_ENGINE_ID,
            "configured": bool(cls.GOOGLE_API_KEY and cls.SEARCH_ENGINE_ID)
        }
    
    @classmethod
    def validate_google_search_config(cls) -> bool:
        """验证Google搜索配置是否完整"""
        return bool(cls.GOOGLE_API_KEY and cls.SEARCH_ENGINE_ID)

# 创建全局配置实例
config = Config()
