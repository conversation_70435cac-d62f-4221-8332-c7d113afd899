import json
import os
import uuid
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class APIConfig:
    """API配置数据类"""
    url: str
    method: str = "POST"
    headers: Dict[str, str] = None
    params: Dict[str, Any] = None
    timeout: int = 30
    
    def __post_init__(self):
        if self.headers is None:
            self.headers = {"Content-Type": "application/json"}
        if self.params is None:
            self.params = {}

@dataclass
class MCPServiceConfig:
    """MCP服务配置数据类"""
    service_id: str
    name: str
    description: str
    api_config: APIConfig
    port: int
    status: str = "stopped"
    tool_description: str = None  # 工具的详细描述，用于生成docstring
    created_at: str = None
    updated_at: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
        if self.updated_at is None:
            self.updated_at = datetime.now().isoformat()

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "configs"):
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, "mcp_services.json")
        self.services: Dict[str, MCPServiceConfig] = {}
        
        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)
        self.load_config()
    
    def load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for service_id, service_data in data.items():
                        # 重构API配置
                        api_config = APIConfig(**service_data['api_config'])
                        service_data['api_config'] = api_config
                        # 创建服务配置
                        self.services[service_id] = MCPServiceConfig(**service_data)
                logger.info(f"已加载 {len(self.services)} 个服务配置")
            else:
                logger.info("配置文件不存在，创建新的配置")
                self.save_config()
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            self.services = {}
    
    def save_config(self):
        """保存配置到文件"""
        try:
            data = {}
            for service_id, service_config in self.services.items():
                service_dict = asdict(service_config)
                data[service_id] = service_dict
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info("配置已保存")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def add_service(self, name: str, description: str, api_config: APIConfig, port: int = None, tool_description: str = None) -> str:
        """添加新服务"""
        service_id = str(uuid.uuid4())
        
        # 如果没有指定端口，自动分配
        if port is None:
            port = self.get_available_port()
        
        service_config = MCPServiceConfig(
            service_id=service_id,
            name=name,
            description=description,
            api_config=api_config,
            port=port,
            tool_description=tool_description
        )
        self.services[service_id] = service_config
        self.save_config()
        logger.info(f"已添加服务: {name} (ID: {service_id}, Port: {port})")
        return service_id
    
    def get_service(self, service_id: str) -> Optional[MCPServiceConfig]:
        """获取服务配置"""
        return self.services.get(service_id)
    
    def get_all_services(self) -> Dict[str, MCPServiceConfig]:
        """获取所有服务配置"""
        return self.services.copy()
    
    def update_service(self, service_id: str, **kwargs) -> bool:
        """更新服务配置"""
        if service_id not in self.services:
            return False
        
        service = self.services[service_id]
        for key, value in kwargs.items():
            if hasattr(service, key):
                setattr(service, key, value)
        
        service.updated_at = datetime.now().isoformat()
        self.save_config()
        logger.info(f"已更新服务: {service_id}")
        return True
    
    def delete_service(self, service_id: str) -> bool:
        """删除服务配置"""
        if service_id in self.services:
            service_name = self.services[service_id].name
            del self.services[service_id]
            self.save_config()
            logger.info(f"已删除服务: {service_name} (ID: {service_id})")
            return True
        return False
    
    def get_available_port(self, start_port: int = 8001) -> int:
        """获取可用端口"""
        used_ports = {service.port for service in self.services.values()}
        port = start_port
        while port in used_ports:
            port += 1
        return port
    
    def get_services_by_status(self, status: str) -> List[MCPServiceConfig]:
        """根据状态获取服务列表"""
        return [service for service in self.services.values() if service.status == status]
    
    def update_service_status(self, service_id: str, status: str) -> bool:
        """更新服务状态"""
        return self.update_service(service_id, status=status)
