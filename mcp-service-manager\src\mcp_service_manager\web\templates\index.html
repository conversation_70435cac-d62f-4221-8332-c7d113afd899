<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP服务管理器</title>
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link href="/static/bootstrap-icons.css" rel="stylesheet">
    <style>
        .service-card {
            transition: transform 0.2s;
        }
        .service-card:hover {
            transform: translateY(-2px);
        }
        .status-running {
            color: #28a745;
        }
        .status-stopped {
            color: #dc3545;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-gear-fill"></i> MCP服务管理器
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bi bi-circle-fill text-success"></i> 系统运行中
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 添加服务表单 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-plus-circle"></i> 添加新服务
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="addServiceForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="serviceName" class="form-label">服务名称</label>
                                        <input type="text" class="form-control" id="serviceName" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="serviceDescription" class="form-label">服务描述</label>
                                        <textarea class="form-control" id="serviceDescription" rows="2" required></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="toolDescription" class="form-label">工具描述 (可选)</label>
                                        <textarea class="form-control" id="toolDescription" rows="4" placeholder="详细描述工具的功能、参数和返回值，帮助AI理解何时使用此工具&#10;&#10;例如：&#10;查询四川省相关政策，在询问政策相关问题时调用&#10;&#10;Args:&#10;    query: 用户问题&#10;    area_code: 地区编码（默认为四川省：5100000）&#10;&#10;Returns:&#10;    包含政策查询结果的字典"></textarea>
                                        <div class="form-text">为AI提供详细的工具使用说明，包括参数说明和使用场景</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="servicePort" class="form-label">端口号</label>
                                        <input type="number" class="form-control" id="servicePort" min="1024" max="65535">
                                        <div class="form-text">留空自动分配端口</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="apiUrl" class="form-label">API URL</label>
                                        <input type="url" class="form-control" id="apiUrl" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="apiMethod" class="form-label">HTTP方法</label>
                                        <select class="form-select" id="apiMethod">
                                            <option value="POST">POST</option>
                                            <option value="GET">GET</option>
                                            <option value="PUT">PUT</option>
                                            <option value="DELETE">DELETE</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="apiHeaders" class="form-label">请求头 (JSON格式)</label>
                                        <textarea class="form-control" id="apiHeaders" rows="3" placeholder='{"Content-Type": "application/json"}'></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="apiParams" class="form-label">默认参数 (JSON格式)</label>
                                        <textarea class="form-control" id="apiParams" rows="2" placeholder='{"param1": "value1"}'></textarea>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus"></i> 创建服务
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服务列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i> 服务列表
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshServices()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="servicesList" class="row">
                            <!-- 服务卡片将在这里动态生成 -->
                        </div>
                        <div id="noServices" class="text-center text-muted" style="display: none;">
                            <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                            <p class="mt-2">暂无服务，请添加新服务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 服务详情模态框 -->
    <div class="modal fade" id="serviceDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">服务详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="serviceDetailContent">
                    <!-- 详情内容将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- Toast通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastBody">
                <!-- 通知内容 -->
            </div>
        </div>
    </div>

    <script src="/static/bootstrap.bundle.min.js"></script>
    <script src="/static/app.js"></script>
</body>
</html>
