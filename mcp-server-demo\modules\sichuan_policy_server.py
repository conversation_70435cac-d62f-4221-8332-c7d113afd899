from fastmcp import FastMCP
import requests
import logging
import json
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建四川省政策查询MCP服务
sichuan_policy_mcp = FastMCP(name="SichuanPolicyService")

@sichuan_policy_mcp.tool()
def query_sichuan_policy(query: str, area_code: str = "5100000") -> Dict[str, Any]:
    """
    查询四川省相关政策，在询问政策相关问题时调用
    
    Args:
        query: 用户问题
        area_code: 地区编码（默认为四川省：5100000）
    
    Returns:
        包含政策查询结果的字典
    """
    url = "http://10.206.23.238/v1/workflows/run"
    api_key = "app-SPJtpNohiogcpYBdI6pkt5CJ"
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 设置请求参数
    payload = {
        "inputs": {
            "query": query,
            "area_code": area_code
        },
        "response_mode": "blocking",
        "user": "abc-123"
    }
    
    try:
        # 使用stream=True来处理流式响应
        response = requests.post(url, headers=headers, json=payload, verify=False, stream=True)
        
        # 检查响应状态
        if response.status_code == 200:
            try:
                # 解析blocking模式的JSON响应
                result = response.json()

                # 检查响应结构
                if isinstance(result, dict):
                    # 获取data字段
                    data_field = result.get("data", {})

                    if isinstance(data_field, dict):
                        # 检查工作流状态
                        status = data_field.get("status", "")

                        if status == "succeeded":
                            # 获取outputs字段
                            outputs = data_field.get("outputs", {})

                            if isinstance(outputs, dict):
                                output_text = outputs.get("output", "")
                                is_spec_find = outputs.get("is_spec_find", "False")

                                return {
                                    "code": "200",
                                    "data": {
                                        "task_id": result.get("task_id", ""),
                                        "workflow_run_id": result.get("workflow_run_id", ""),
                                        "query": query,
                                        "area_code": area_code,
                                        "output": output_text,
                                        "is_spec_find": is_spec_find,
                                        "found_policy": is_spec_find == "True",
                                        "status": status,
                                        "elapsed_time": data_field.get("elapsed_time", 0),
                                        "total_steps": data_field.get("total_steps", 0)
                                    },
                                    "msg": "查询成功"
                                }
                            else:
                                return {
                                    "code": "error",
                                    "msg": f"响应格式错误：outputs字段不是字典类型"
                                }
                        elif status == "failed":
                            error_info = data_field.get("error", "未知错误")
                            return {
                                "code": "error",
                                "msg": f"工作流执行失败: {error_info}"
                            }
                        else:
                            return {
                                "code": "error",
                                "msg": f"工作流状态异常: {status}"
                            }
                    else:
                        return {
                            "code": "error",
                            "msg": "响应格式错误：data字段不是字典类型"
                        }
                else:
                    return {
                        "code": "error",
                        "msg": "响应格式错误：根对象不是字典类型"
                    }

            except json.JSONDecodeError as e:
                logging.error(f"JSON解析失败: {str(e)}")
                return {
                    "code": "error",
                    "msg": f"JSON解析失败: {str(e)}，响应内容: {response.text[:200]}..."
                }
            except Exception as parse_error:
                logging.error(f"响应解析错误: {str(parse_error)}")
                return {
                    "code": "error",
                    "msg": f"响应解析错误: {str(parse_error)}"
                }
        else:
            logging.error(f"政策查询API请求失败，状态码: {response.status_code}")
            return {
                "code": "error", 
                "msg": f"政策查询API请求失败，状态码: {response.status_code}，响应: {response.text[:200]}"
            }
            
    except Exception as e:
        logging.error(f"政策查询请求过程中发生错误: {str(e)}")
        return {"code": "error", "msg": f"政策查询请求过程中发生错误: {str(e)}"}

    """
    获取政策查询服务状态
    
    Returns:
        服务状态信息
    """
    try:
        # 简单的健康检查
        test_url = "http://10.206.23.238/v1/workflows/run"
        response = requests.head(test_url, timeout=5)
        
        if response.status_code in [200, 405]:  # 405 Method Not Allowed 也表示服务可用
            return {"status": "online", "msg": "政策查询服务正常"}
        else:
            return {"status": "error", "msg": f"政策查询服务异常，状态码: {response.status_code}"}
    except Exception as e:
        return {"status": "offline", "msg": f"政策查询服务不可用: {str(e)}"}





