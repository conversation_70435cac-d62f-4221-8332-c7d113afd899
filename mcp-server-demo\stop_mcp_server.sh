#!/bin/bash

# MCP服务器停止脚本
# 用法: ./stop_mcp_server.sh [选项]
# 选项:
#   --force                 强制终止进程
#   --timeout <seconds>     等待超时时间 (默认: 30秒)
#   --help                  显示帮助信息

set -e  # 遇到错误时退出

# 默认配置
DEFAULT_FORCE=false
DEFAULT_TIMEOUT=30
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/mcp_server.pid"
LOG_FILE="$SCRIPT_DIR/mcp_server.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
MCP服务器停止脚本

用法: $0 [选项]

选项:
    --force                 强制终止进程 (使用SIGKILL)
    --timeout <seconds>     等待进程终止的超时时间 (默认: 30秒)
    --help                  显示此帮助信息

示例:
    $0                      # 正常停止服务器
    $0 --force              # 强制停止服务器
    $0 --timeout 60         # 设置60秒超时时间

EOF
}

# 检查PID文件
check_pid_file() {
    if [[ ! -f "$PID_FILE" ]]; then
        log_error "PID文件不存在: $PID_FILE"
        log_error "MCP服务器可能未运行或未通过启动脚本启动"
        exit 1
    fi
    
    local pid=$(cat "$PID_FILE")
    if ! [[ "$pid" =~ ^[0-9]+$ ]]; then
        log_error "PID文件包含无效的进程ID: $pid"
        rm -f "$PID_FILE"
        exit 1
    fi
    
    if ! kill -0 "$pid" 2>/dev/null; then
        log_warn "进程 $pid 不存在，清理PID文件..."
        rm -f "$PID_FILE"
        exit 0
    fi
    
    echo "$pid"
}

# 停止服务器
stop_server() {
    local pid="$1"
    local force="$2"
    local timeout="$3"
    
    log_info "停止MCP服务器 (PID: $pid)..."
    
    if [[ "$force" == "true" ]]; then
        log_warn "强制终止进程..."
        kill -9 "$pid"
        rm -f "$PID_FILE"
        log_info "MCP服务器已强制停止"
        return 0
    fi
    
    # 发送SIGTERM信号
    log_info "发送终止信号 (SIGTERM)..."
    kill -15 "$pid"
    
    # 等待进程终止
    log_info "等待进程终止 (最多 $timeout 秒)..."
    local count=0
    while kill -0 "$pid" 2>/dev/null; do
        sleep 1
        count=$((count + 1))
        
        if [[ $count -ge $timeout ]]; then
            log_warn "等待超时，进程未终止"
            log_warn "如需强制终止，请使用 --force 选项"
            return 1
        fi
        
        if [[ $((count % 5)) -eq 0 ]]; then
            log_info "仍在等待进程终止... ($count/$timeout)"
        fi
    done
    
    log_info "MCP服务器已停止"
    rm -f "$PID_FILE"
    return 0
}

# 清理资源
cleanup() {
    log_info "清理资源..."
    
    # 备份日志文件
    if [[ -f "$LOG_FILE" ]]; then
        local timestamp=$(date '+%Y%m%d%H%M%S')
        local backup_log="$LOG_FILE.$timestamp"
        log_info "备份日志文件到: $backup_log"
        cp "$LOG_FILE" "$backup_log"
    fi
    
    # 删除临时文件
    log_info "清理完成"
}

# 主函数
main() {
    local force="$DEFAULT_FORCE"
    local timeout="$DEFAULT_TIMEOUT"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force="true"
                shift
                ;;
            --timeout)
                timeout="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "开始停止MCP服务器..."
    
    # 检查PID文件并获取进程ID
    local pid=$(check_pid_file)
    
    # 停止服务器
    if stop_server "$pid" "$force" "$timeout"; then
        cleanup
        log_info "MCP服务器已成功停止"
    else
        log_error "无法停止MCP服务器"
        exit 1
    fi
}

# 捕获信号
trap 'log_warn "收到中断信号，退出..."; exit 130' INT TERM

# 运行主函数
main "$@"
