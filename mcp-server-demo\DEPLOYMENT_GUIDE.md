# MCP服务器部署指南

本指南介绍如何在服务器环境中部署和管理MCP服务器。

## 文件说明

### 启动和停止脚本

| 文件名 | 平台 | 描述 |
|--------|------|------|
| `start_mcp_server.sh` | Linux/Unix | Linux/Unix系统启动脚本 |
| `stop_mcp_server.sh` | Linux/Unix | Linux/Unix系统停止脚本 |
| `start_mcp_server.bat` | Windows | Windows系统启动脚本 |
| `stop_mcp_server.bat` | Windows | Windows系统停止脚本 |
| `server_config.conf` | 通用 | 服务器配置文件 |

## 快速开始

### Linux/Unix系统

1. **设置脚本权限**
```bash
chmod +x start_mcp_server.sh stop_mcp_server.sh
```

2. **启动服务器**
```bash
# 启动所有服务（前台运行）
./start_mcp_server.sh

# 后台运行
./start_mcp_server.sh --daemon

# 指定服务和端口
./start_mcp_server.sh --services social,policy --port 9000 --daemon
```

3. **停止服务器**
```bash
# 正常停止
./stop_mcp_server.sh

# 强制停止
./stop_mcp_server.sh --force
```

### Windows系统

1. **启动服务器**
```cmd
REM 启动所有服务（前台运行）
start_mcp_server.bat

REM 后台运行
start_mcp_server.bat --daemon

REM 指定服务和端口
start_mcp_server.bat --services social,policy --port 9000 --daemon
```

2. **停止服务器**
```cmd
REM 正常停止
stop_mcp_server.bat

REM 强制停止
stop_mcp_server.bat --force
```

## 配置文件

### 基本配置

编辑 `server_config.conf` 文件来自定义服务器配置：

```bash
# 服务列表
MCP_SERVICES="all"  # 或 "social,policy,service-guide"

# 网络配置
MCP_PORT=8000
MCP_HOST="0.0.0.0"

# 运行模式
MCP_DAEMON="false"  # true为后台运行
```

### 生产环境配置示例

```bash
# 生产环境推荐配置
MCP_SERVICES="social,policy,service-guide"
MCP_PORT=8000
MCP_HOST="0.0.0.0"
MCP_DAEMON="true"
LOG_LEVEL="WARNING"
ENABLE_API_KEY="true"
API_KEY="your-secure-api-key-here"
```

## 命令行选项

### 启动脚本选项

| 选项 | 描述 | 默认值 |
|------|------|--------|
| `--services` | 要启动的服务列表 | `all` |
| `--port` | 服务端口 | `8000` |
| `--host` | 主机地址 | `0.0.0.0` |
| `--daemon` | 后台运行模式 | `false` |
| `--config` | 配置文件路径 | `server_config.conf` |
| `--help` | 显示帮助信息 | - |

### 停止脚本选项

| 选项 | 描述 | 默认值 |
|------|------|--------|
| `--force` | 强制终止进程 | `false` |
| `--timeout` | 等待超时时间（秒） | `30` |
| `--help` | 显示帮助信息 | - |

## 服务管理

### 检查服务状态

```bash
# Linux/Unix
ps aux | grep main_modular.py

# Windows
tasklist | findstr python.exe
```

### 查看日志

```bash
# 实时查看日志
tail -f mcp_server.log

# Windows
type mcp_server.log
```

### 重启服务

```bash
# Linux/Unix
./stop_mcp_server.sh && ./start_mcp_server.sh --daemon

# Windows
stop_mcp_server.bat && start_mcp_server.bat --daemon
```

## 部署场景

### 1. 开发环境

```bash
# 前台运行，便于调试
./start_mcp_server.sh --services all --port 8000
```

### 2. 测试环境

```bash
# 后台运行，指定服务
./start_mcp_server.sh --services social,policy --port 8000 --daemon
```

### 3. 生产环境

```bash
# 使用配置文件，后台运行
./start_mcp_server.sh --config /etc/mcp/production.conf --daemon
```

### 4. 多实例部署

```bash
# 实例1：社保服务
./start_mcp_server.sh --services social --port 8001 --daemon

# 实例2：政策服务
./start_mcp_server.sh --services policy --port 8002 --daemon

# 实例3：办事指南服务
./start_mcp_server.sh --services service-guide --port 8003 --daemon
```

## 监控和维护

### 健康检查

```bash
# 检查服务是否响应
curl http://localhost:8000/health

# 检查可用服务
curl -X POST http://localhost:8000/tools/get_available_services \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 日志轮转

脚本会自动备份日志文件，格式为：`mcp_server.log.YYYYMMDDHHMMSS`

### 性能监控

```bash
# 监控进程资源使用
top -p $(cat mcp_server.pid)

# Windows
tasklist /fi "pid eq $(type mcp_server.pid)"
```

## 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查找占用端口的进程
netstat -tulpn | grep :8000
# 或
ss -tulpn | grep :8000
```

2. **虚拟环境问题**
```bash
# 重新创建虚拟环境
rm -rf .venv
uv venv
source .venv/bin/activate  # Linux/Unix
# 或 .venv\Scripts\activate  # Windows
uv add fastmcp
```

3. **权限问题**
```bash
# Linux/Unix
chmod +x *.sh
chown user:group mcp_server.log
```

4. **进程无法停止**
```bash
# 强制停止
./stop_mcp_server.sh --force

# 手动清理
rm -f mcp_server.pid
```

### 日志分析

查看常见错误模式：
```bash
# 查看错误日志
grep -i error mcp_server.log

# 查看启动日志
grep -i "启动\|start" mcp_server.log

# 查看最近的日志
tail -n 100 mcp_server.log
```

## 安全建议

1. **网络安全**
   - 在生产环境中考虑使用防火墙限制访问
   - 启用API密钥验证
   - 使用HTTPS代理（如nginx）

2. **文件权限**
   - 限制配置文件的读取权限
   - 确保日志文件有适当的写入权限

3. **进程管理**
   - 使用非root用户运行服务
   - 考虑使用systemd或其他进程管理器

## 系统服务集成

### systemd服务（Linux）

创建 `/etc/systemd/system/mcp-server.service`：

```ini
[Unit]
Description=MCP Server
After=network.target

[Service]
Type=forking
User=mcp
Group=mcp
WorkingDirectory=/path/to/mcp-server-demo
ExecStart=/path/to/mcp-server-demo/start_mcp_server.sh --daemon
ExecStop=/path/to/mcp-server-demo/stop_mcp_server.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl enable mcp-server
sudo systemctl start mcp-server
sudo systemctl status mcp-server
```

### Windows服务

可以使用NSSM（Non-Sucking Service Manager）将脚本注册为Windows服务。

## 备份和恢复

### 备份重要文件

```bash
# 创建备份目录
mkdir -p backup/$(date +%Y%m%d)

# 备份配置和日志
cp server_config.conf backup/$(date +%Y%m%d)/
cp mcp_server.log* backup/$(date +%Y%m%d)/
```

### 恢复配置

```bash
# 恢复配置文件
cp backup/20240101/server_config.conf ./
```

这些脚本提供了完整的MCP服务器管理解决方案，支持开发、测试和生产环境的不同需求。
