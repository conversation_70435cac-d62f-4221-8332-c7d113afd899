{"28930cb9-7ac5-48cf-bc5c-b1a168baecda": {"service_id": "28930cb9-7ac5-48cf-bc5c-b1a168ba<PERSON>da", "name": "四川省政策查询服务", "description": "查询四川省相关政策信息的MCP服务", "api_config": {"url": "http://*************/v1/workflows/run", "method": "POST", "headers": {"Authorization": "Bearer app-SPJtpNohiogcpYBdI6pkt5CJ", "Content-Type": "application/json"}, "params": {"inputs": {"query": "", "area_code": "5100000"}, "response_mode": "blocking", "user": "abc-123"}, "timeout": 30}, "port": 15235, "status": "running", "tool_description": "查询四川省相关政策，在询问政策相关问题时调用。参数：query(用户问题)，area_code(地区编码，默认为四川省：5100000)", "created_at": "2025-07-29T15:32:45.146501", "updated_at": "2025-07-29T15:58:58.391296"}, "2699649c-0fe2-4d08-830e-063a2b2606c0": {"service_id": "2699649c-0fe2-4d08-830e-063a2b2606c0", "name": "查询地区", "description": "查询地区树", "api_config": {"url": "https://************/gateway3/online-service/chatSessionRecord/getAreaTree", "method": "POST", "headers": {"Content-Type": "application/json"}, "params": {"areaLevel": "3", "parentAreaId": "1431"}, "timeout": 30}, "port": 15238, "status": "stopped", "tool_description": " 查询地区信息树\n    \n     Args:\n         area_level: 地区级别（1=省，2=市，3=县，4=镇，默认为3）\n        parent_area_id: 父级地区ID（1431为四川省ID，默认为1431）\n    \n     Returns:\n         地区信息树结果", "created_at": "2025-07-29T15:50:21.973790", "updated_at": "2025-09-16T13:35:31.801365"}}