from fastmcp import FastMCP
import math
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建计算器MCP服务
calculator_mcp = FastMCP(name="CalculatorService")

# 基础算术工具组
@calculator_mcp.tool()
def add(a: float, b: float) -> float:
    """执行浮点数加法运算"""
    return a + b

@calculator_mcp.tool()
def subtract(a: float, b: float) -> float:
    """执行浮点数减法运算"""
    return a - b

@calculator_mcp.tool()
def multiply(a: float, b: float) -> float:
    """执行浮点数乘法运算"""
    return a * b

@calculator_mcp.tool() 
def divide(a: float, b: float) -> float:
    """执行浮点数除法运算
    Args:
        a: 被除数
        b: 除数（必须非零）
    """
    if b == 0:
        raise ValueError("除数不能为零")
    return a / b

# 高级数学工具
@calculator_mcp.tool()
def power(base: float, exponent: float) -> float:
    """计算幂运算"""
    return base ** exponent

@calculator_mcp.tool()
def sqrt(number: float) -> float:
    """计算平方根"""
    if number < 0:
        raise ValueError("不能计算负数的平方根")
    return math.sqrt(number)

@calculator_mcp.tool()
def factorial(n: int) -> int:
    """计算整数阶乘"""
    if n < 0:
        raise ValueError("不能计算负数的阶乘")
    return math.factorial(n)

@calculator_mcp.tool()
def sin(angle: float) -> float:
    """计算正弦值（角度为弧度）"""
    return math.sin(angle)

@calculator_mcp.tool()
def cos(angle: float) -> float:
    """计算余弦值（角度为弧度）"""
    return math.cos(angle)

@calculator_mcp.tool()
def tan(angle: float) -> float:
    """计算正切值（角度为弧度）"""
    return math.tan(angle)

@calculator_mcp.tool()
def log(number: float, base: float = math.e) -> float:
    """计算对数"""
    if number <= 0:
        raise ValueError("对数的真数必须大于0")
    if base <= 0 or base == 1:
        raise ValueError("对数的底数必须大于0且不等于1")
    return math.log(number, base)

# 资源：数学常数
@calculator_mcp.resource("math://constants")
def get_math_constants() -> dict:
    """提供常用数学常数"""
    return {
        "pi": math.pi,
        "e": math.e,
        "tau": math.tau,
        "inf": math.inf
    }
