import logging
from typing import Dict, List, Optional, Any
from .config_manager import Config<PERSON>anager, MCPServiceConfig, APIConfig
from .service_generator import ServiceGenerator

logger = logging.getLogger(__name__)

class ServiceManager:
    """服务管理器 - 统一管理配置和服务生成"""
    
    def __init__(self, config_dir: str = "configs"):
        self.config_manager = ConfigManager(config_dir)
        self.service_generator = ServiceGenerator()
    
    def create_service(self, name: str, description: str, api_config_dict: Dict[str, Any], port: int = None, tool_description: str = None) -> Dict[str, Any]:
        """创建新的MCP服务"""
        try:
            # 创建API配置对象
            api_config = APIConfig(**api_config_dict)
            
            # 添加服务配置
            service_id = self.config_manager.add_service(name, description, api_config, port, tool_description)
            
            return {
                "success": True,
                "service_id": service_id,
                "message": f"服务 '{name}' 创建成功"
            }
        except Exception as e:
            logger.error(f"创建服务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def start_service(self, service_id: str) -> Dict[str, Any]:
        """启动MCP服务"""
        try:
            config = self.config_manager.get_service(service_id)
            if not config:
                return {
                    "success": False,
                    "error": "服务不存在"
                }
            
            # 检查服务是否已经在运行
            if self.service_generator.is_service_running(service_id):
                return {
                    "success": False,
                    "error": "服务已在运行"
                }
            
            # 启动服务
            success = self.service_generator.start_service(config)
            if success:
                # 更新服务状态
                self.config_manager.update_service_status(service_id, "running")
                return {
                    "success": True,
                    "message": f"服务 '{config.name}' 启动成功",
                    "port": config.port
                }
            else:
                return {
                    "success": False,
                    "error": "服务启动失败"
                }
        except Exception as e:
            logger.error(f"启动服务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def stop_service(self, service_id: str) -> Dict[str, Any]:
        """停止MCP服务"""
        try:
            config = self.config_manager.get_service(service_id)
            if not config:
                return {
                    "success": False,
                    "error": "服务不存在"
                }
            
            # 停止服务
            success = self.service_generator.stop_service(service_id)
            if success:
                # 更新服务状态
                self.config_manager.update_service_status(service_id, "stopped")
                return {
                    "success": True,
                    "message": f"服务 '{config.name}' 停止成功"
                }
            else:
                return {
                    "success": False,
                    "error": "服务停止失败"
                }
        except Exception as e:
            logger.error(f"停止服务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def delete_service(self, service_id: str) -> Dict[str, Any]:
        """删除MCP服务"""
        try:
            config = self.config_manager.get_service(service_id)
            if not config:
                return {
                    "success": False,
                    "error": "服务不存在"
                }
            
            # 如果服务正在运行，先停止
            if self.service_generator.is_service_running(service_id):
                self.service_generator.stop_service(service_id)
            
            # 清理服务文件
            self.service_generator.cleanup_service_file(service_id)
            
            # 删除配置
            success = self.config_manager.delete_service(service_id)
            if success:
                return {
                    "success": True,
                    "message": f"服务 '{config.name}' 删除成功"
                }
            else:
                return {
                    "success": False,
                    "error": "删除服务配置失败"
                }
        except Exception as e:
            logger.error(f"删除服务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_service_info(self, service_id: str) -> Dict[str, Any]:
        """获取服务详细信息"""
        try:
            config = self.config_manager.get_service(service_id)
            if not config:
                return {
                    "success": False,
                    "error": "服务不存在"
                }
            
            # 获取运行状态
            is_running = self.service_generator.is_service_running(service_id)
            status_info = self.service_generator.get_service_status(service_id)
            
            return {
                "success": True,
                "service": {
                    "service_id": config.service_id,
                    "name": config.name,
                    "description": config.description,
                    "port": config.port,
                    "status": "running" if is_running else "stopped",
                    "api_config": {
                        "url": config.api_config.url,
                        "method": config.api_config.method,
                        "headers": config.api_config.headers,
                        "params": config.api_config.params,
                        "timeout": config.api_config.timeout
                    },
                    "created_at": config.created_at,
                    "updated_at": config.updated_at,
                    "runtime_info": status_info
                }
            }
        except Exception as e:
            logger.error(f"获取服务信息失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def list_services(self) -> Dict[str, Any]:
        """获取所有服务列表"""
        try:
            services = []
            for service_id, config in self.config_manager.get_all_services().items():
                is_running = self.service_generator.is_service_running(service_id)
                services.append({
                    "service_id": config.service_id,
                    "name": config.name,
                    "description": config.description,
                    "port": config.port,
                    "status": "running" if is_running else "stopped",
                    "api_url": config.api_config.url,
                    "created_at": config.created_at
                })
            
            return {
                "success": True,
                "services": services,
                "total": len(services)
            }
        except Exception as e:
            logger.error(f"获取服务列表失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def update_service_config(self, service_id: str, **kwargs) -> Dict[str, Any]:
        """更新服务配置"""
        try:
            config = self.config_manager.get_service(service_id)
            if not config:
                return {
                    "success": False,
                    "error": "服务不存在"
                }
            
            # 如果服务正在运行，需要重启
            was_running = self.service_generator.is_service_running(service_id)
            if was_running:
                self.service_generator.stop_service(service_id)
            
            # 更新配置
            success = self.config_manager.update_service(service_id, **kwargs)
            
            # 如果之前在运行，重新启动
            if was_running and success:
                updated_config = self.config_manager.get_service(service_id)
                self.service_generator.start_service(updated_config)
                self.config_manager.update_service_status(service_id, "running")
            
            if success:
                return {
                    "success": True,
                    "message": "服务配置更新成功"
                }
            else:
                return {
                    "success": False,
                    "error": "更新服务配置失败"
                }
        except Exception as e:
            logger.error(f"更新服务配置失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_available_port(self) -> int:
        """获取可用端口"""
        return self.config_manager.get_available_port()
    
    def shutdown(self):
        """关闭服务管理器，停止所有服务"""
        logger.info("正在关闭服务管理器...")
        self.service_generator.stop_all_services()
        logger.info("所有服务已停止")
