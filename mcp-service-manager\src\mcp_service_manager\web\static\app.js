// MCP服务管理器前端JavaScript

class MCPServiceManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadServices();
        this.setupFormDefaults();
    }

    setupEventListeners() {
        // 添加服务表单提交
        document.getElementById('addServiceForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createService();
        });
    }

    setupFormDefaults() {
        // 设置默认请求头
        document.getElementById('apiHeaders').value = JSON.stringify({
            "Content-Type": "application/json"
        }, null, 2);
        
        // 设置默认参数
        document.getElementById('apiParams').value = JSON.stringify({}, null, 2);
    }

    async createService() {
        try {
            const formData = this.getFormData();
            
            const response = await fetch('/api/services', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();

            if (response.ok) {
                this.showToast('服务创建成功！', 'success');
                this.clearForm();
                this.loadServices();
            } else {
                this.showToast(`创建失败: ${result.detail}`, 'error');
            }
        } catch (error) {
            this.showToast(`创建失败: ${error.message}`, 'error');
        }
    }

    getFormData() {
        const name = document.getElementById('serviceName').value;
        const description = document.getElementById('serviceDescription').value;
        const toolDescription = document.getElementById('toolDescription').value;
        const port = document.getElementById('servicePort').value;
        const apiUrl = document.getElementById('apiUrl').value;
        const apiMethod = document.getElementById('apiMethod').value;
        
        let headers, params;
        try {
            headers = JSON.parse(document.getElementById('apiHeaders').value || '{}');
        } catch (e) {
            throw new Error('请求头格式错误，请输入有效的JSON');
        }
        
        try {
            params = JSON.parse(document.getElementById('apiParams').value || '{}');
        } catch (e) {
            throw new Error('参数格式错误，请输入有效的JSON');
        }

        const formData = {
            name,
            description,
            api_config: {
                url: apiUrl,
                method: apiMethod,
                headers,
                params,
                timeout: 30
            }
        };

        if (toolDescription) {
            formData.tool_description = toolDescription;
        }

        if (port) {
            formData.port = parseInt(port);
        }

        return formData;
    }

    clearForm() {
        document.getElementById('addServiceForm').reset();
        this.setupFormDefaults();
    }

    async loadServices() {
        try {
            const response = await fetch('/api/services');
            const result = await response.json();

            if (response.ok) {
                this.renderServices(result.services);
            } else {
                this.showToast(`加载服务失败: ${result.detail}`, 'error');
            }
        } catch (error) {
            this.showToast(`加载服务失败: ${error.message}`, 'error');
        }
    }

    renderServices(services) {
        const container = document.getElementById('servicesList');
        const noServicesDiv = document.getElementById('noServices');

        if (services.length === 0) {
            container.innerHTML = '';
            noServicesDiv.style.display = 'block';
            return;
        }

        noServicesDiv.style.display = 'none';
        container.innerHTML = services.map(service => this.createServiceCard(service)).join('');
    }

    createServiceCard(service) {
        const statusClass = service.status === 'running' ? 'status-running' : 'status-stopped';
        const statusIcon = service.status === 'running' ? 'play-circle-fill' : 'stop-circle-fill';
        const actionButton = service.status === 'running' 
            ? `<button class="btn btn-warning btn-sm" onclick="mcpManager.stopService('${service.service_id}')">
                 <i class="bi bi-stop-circle"></i> 停止
               </button>`
            : `<button class="btn btn-success btn-sm" onclick="mcpManager.startService('${service.service_id}')">
                 <i class="bi bi-play-circle"></i> 启动
               </button>`;

        return `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card service-card h-100">
                    <div class="card-body">
                        <h6 class="card-title">${service.name}</h6>
                        <p class="card-text text-muted small">${service.description}</p>
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-link-45deg"></i> ${service.api_url}
                            </small>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-hdd-network"></i> 端口: ${service.port}
                            </small>
                        </div>
                        ${service.status === 'running' ? `
                        <div class="mb-2">
                            <small class="text-success">
                                <i class="bi bi-cloud-arrow-up"></i> MCP地址:
                                <code class="text-success">http://localhost:${service.port}/mcp</code>
                                <button class="btn btn-outline-success btn-sm ms-1" onclick="mcpManager.copyToClipboard('http://localhost:${service.port}/mcp')" title="复制MCP地址">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </small>
                        </div>` : ''}
                        <div class="mb-3">
                            <span class="badge ${service.status === 'running' ? 'bg-success' : 'bg-secondary'}">
                                <i class="bi bi-${statusIcon}"></i> ${service.status === 'running' ? '运行中' : '已停止'}
                            </span>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100" role="group">
                            ${actionButton}
                            <button class="btn btn-info btn-sm" onclick="mcpManager.showServiceDetail('${service.service_id}')">
                                <i class="bi bi-info-circle"></i> 详情
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="mcpManager.deleteService('${service.service_id}')">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async startService(serviceId) {
        try {
            const response = await fetch(`/api/services/${serviceId}/start`, {
                method: 'POST'
            });

            const result = await response.json();

            if (response.ok) {
                this.showToast('服务启动成功！', 'success');
                this.loadServices();
            } else {
                this.showToast(`启动失败: ${result.detail}`, 'error');
            }
        } catch (error) {
            this.showToast(`启动失败: ${error.message}`, 'error');
        }
    }

    async stopService(serviceId) {
        try {
            const response = await fetch(`/api/services/${serviceId}/stop`, {
                method: 'POST'
            });

            const result = await response.json();

            if (response.ok) {
                this.showToast('服务停止成功！', 'success');
                this.loadServices();
            } else {
                this.showToast(`停止失败: ${result.detail}`, 'error');
            }
        } catch (error) {
            this.showToast(`停止失败: ${error.message}`, 'error');
        }
    }

    async deleteService(serviceId) {
        if (!confirm('确定要删除这个服务吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetch(`/api/services/${serviceId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (response.ok) {
                this.showToast('服务删除成功！', 'success');
                this.loadServices();
            } else {
                this.showToast(`删除失败: ${result.detail}`, 'error');
            }
        } catch (error) {
            this.showToast(`删除失败: ${error.message}`, 'error');
        }
    }

    async showServiceDetail(serviceId) {
        try {
            const response = await fetch(`/api/services/${serviceId}`);
            const result = await response.json();

            if (response.ok) {
                this.renderServiceDetail(result.service);
                const modal = new bootstrap.Modal(document.getElementById('serviceDetailModal'));
                modal.show();
            } else {
                this.showToast(`获取服务详情失败: ${result.detail}`, 'error');
            }
        } catch (error) {
            this.showToast(`获取服务详情失败: ${error.message}`, 'error');
        }
    }

    renderServiceDetail(service) {
        const content = document.getElementById('serviceDetailContent');
        const statusBadge = service.status === 'running'
            ? '<span class="badge bg-success">运行中</span>'
            : '<span class="badge bg-secondary">已停止</span>';

        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td><strong>服务ID:</strong></td><td><code>${service.service_id}</code></td></tr>
                        <tr><td><strong>名称:</strong></td><td>${service.name}</td></tr>
                        <tr><td><strong>描述:</strong></td><td>${service.description}</td></tr>
                        <tr><td><strong>端口:</strong></td><td>${service.port}</td></tr>
                        <tr><td><strong>状态:</strong></td><td>${statusBadge}</td></tr>
                        <tr><td><strong>创建时间:</strong></td><td>${new Date(service.created_at).toLocaleString()}</td></tr>
                        <tr><td><strong>更新时间:</strong></td><td>${new Date(service.updated_at).toLocaleString()}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>API配置</h6>
                    <table class="table table-sm">
                        <tr><td><strong>URL:</strong></td><td><code>${service.api_config.url}</code></td></tr>
                        <tr><td><strong>方法:</strong></td><td><span class="badge bg-info">${service.api_config.method}</span></td></tr>
                        <tr><td><strong>超时:</strong></td><td>${service.api_config.timeout}秒</td></tr>
                    </table>

                    <h6>请求头</h6>
                    <div class="code-block">
                        <pre>${JSON.stringify(service.api_config.headers, null, 2)}</pre>
                    </div>

                    <h6>默认参数</h6>
                    <div class="code-block">
                        <pre>${JSON.stringify(service.api_config.params, null, 2)}</pre>
                    </div>
                </div>
            </div>

            ${service.runtime_info && service.runtime_info.running ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>运行时信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>进程ID:</strong></td><td>${service.runtime_info.pid}</td></tr>
                            <tr><td><strong>服务地址:</strong></td><td><a href="http://localhost:${service.port}" target="_blank">http://localhost:${service.port}</a></td></tr>
                        </table>
                    </div>
                </div>
            ` : ''}
        `;
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastBody = document.getElementById('toastBody');

        // 设置消息内容
        toastBody.textContent = message;

        // 设置样式
        toast.className = 'toast';
        if (type === 'success') {
            toast.classList.add('bg-success', 'text-white');
        } else if (type === 'error') {
            toast.classList.add('bg-danger', 'text-white');
        } else {
            toast.classList.add('bg-info', 'text-white');
        }

        // 显示Toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    // 复制到剪贴板功能
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showAlert('MCP地址已复制到剪贴板', 'success');
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showAlert('MCP地址已复制到剪贴板', 'success');
        }
    }
}

// 全局函数
function refreshServices() {
    mcpManager.loadServices();
}

// 初始化应用
const mcpManager = new MCPServiceManager();
