from fastmcp import FastMCP
import asyncio
import argparse
import logging
from typing import List, Optional

# 导入模块化的服务
from modules.social_security_server import social_security_mcp
from modules.sichuan_policy_server import sichuan_policy_mcp
from modules.service_guide_server import service_guide_mcp

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建主应用
app = FastMCP(name="ModularMCPApplication")

# 全局变量来跟踪已启动的服务
active_services = []

# 异步设置函数
async def setup_services(services: Optional[List[str]] = None):
    """
    设置并导入指定的服务
    
    Args:
        services: 要启动的服务列表，如果为None则启动所有服务
    """
    available_services = {
        "social": social_security_mcp,
        "policy": sichuan_policy_mcp,
        "service-guide": service_guide_mcp
    }
    
    if services is None:
        services = list(available_services.keys())
    
    for service_name in services:
        if service_name in available_services:
            await app.import_server(service_name, available_services[service_name])
            active_services.append(service_name)
            logging.info(f"已导入服务: {service_name}")
        else:
            logging.warning(f"未知服务: {service_name}")

@app.tool()
def get_available_services() -> dict:
    """获取当前启动的服务列表"""
    # 定义所有服务的详细信息
    all_services_info = {
        "social": {
            "name": "social",
            "description": "社保查询服务，提供个人社保证明和权益信息查询功能",
            "tools": ["query_social_certificate", "query_social_equity", "validate_token", "get_service_status"]
        },
        "policy": {
            "name": "policy",
            "description": "四川省政策查询服务，提供政策文件查询和解读功能",
            "tools": ["query_sichuan_policy", "validate_area_code", "get_policy_service_status"]
        },
        "service-guide": {
            "name": "service-guide",
            "description": "办事指南查询服务，提供事项搜索、地区查询和办事详情功能",
            "tools": ["search_service_events", "get_area_tree", "search_event_detail", "validate_area_code", "get_service_status"]
        }
    }

    # 只返回当前启动的服务
    active_services_info = []
    for service_name in active_services:
        if service_name in all_services_info:
            active_services_info.append(all_services_info[service_name])

    return {
        "services": active_services_info
    }


def main():
    """主函数，支持命令行参数"""
    parser = argparse.ArgumentParser(description="模块化MCP服务器")
    parser.add_argument(
        "--services",
        nargs="*",
        choices=["calculator", "social", "policy", "service-guide", "all"],
        default=["all"],
        help="要启动的服务列表 (默认: all)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000,
        help="服务端口 (默认: 8000)"
    )
    parser.add_argument(
        "--host", 
        default="0.0.0.0",
        help="服务主机地址 (默认: 0.0.0.0)"
    )
    
    args = parser.parse_args()
    
    # 处理服务列表
    if "all" in args.services:
        services_to_start = None  # 启动所有服务
    else:
        services_to_start = args.services
    
    # 运行异步设置
    asyncio.run(setup_services(services_to_start))
    
    # 启动服务器
    print(f"启动模块化MCP服务器")
    print(f"地址: http://{args.host}:{args.port}")
    if services_to_start:
        print(f"启动的服务: {', '.join(services_to_start)}")
    else:
        print("启动的服务: 所有服务")
    
    app.run(transport="http", host=args.host, port=args.port)

if __name__ == "__main__":
    main()
