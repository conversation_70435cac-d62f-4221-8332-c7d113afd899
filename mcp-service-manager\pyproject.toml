[project]
name = "mcp-service-manager"
version = "0.1.0"
description = "可配置化的MCP服务管理系统"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "mcp[cli]",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "requests>=2.31.0",
    "pydantic>=2.0.0",
    "jinja2>=3.1.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.0.0",
    "fastmcp>=2.10.6",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_service_manager"]

[project.scripts]
mcp-service-manager = "mcp_service_manager.main:main"
