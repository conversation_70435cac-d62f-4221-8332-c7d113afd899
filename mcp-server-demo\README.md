# 模块化MCP服务

这是一个模块化的MCP（Model Context Protocol）服务架构，支持同时启动多个MCP服务或单独启动单个MCP服务。

## 项目结构

```
mcp-server-demo/
├── modules/                          # 模块化服务目录
│   ├── __init__.py                   # 模块初始化文件
│   ├── calculator_server.py          # 计算器服务模块
│   ├── social_security_server.py     # 社保查询服务模块
│   ├── sichuan_policy_server.py      # 四川省政策查询服务模块
│   └── service_guide_server.py       # 办事指南查询服务模块
├── main_modular.py                   # 主程序（支持多服务）
├── run_calculator.py                 # 单独启动计算器服务
├── run_social_security.py            # 单独启动社保查询服务
├── server.py                         # 原始计算器服务（保留）
├── social_security_service.py        # 原始社保服务（保留）
└── README.md                         # 本文档
```

2. 安装依赖

# python 版本 要求 >= 3.10
# 使用 uv 来管理项目
# 初始化项目
uv init mcp-server-demo
cd mcp-server-demo

# 创建并激活环境
uv venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate

# 安装mcp 依赖
uv add "mcp[cli]"

## 服务模块

### 1. 计算器服务 (calculator_server.py)

提供数学计算功能：

**工具 (Tools):**
- `add(a, b)` - 加法运算
- `subtract(a, b)` - 减法运算
- `multiply(a, b)` - 乘法运算
- `divide(a, b)` - 除法运算
- `power(base, exponent)` - 幂运算
- `sqrt(number)` - 平方根
- `factorial(n)` - 阶乘
- `sin(angle)` - 正弦值
- `cos(angle)` - 余弦值
- `tan(angle)` - 正切值
- `log(number, base)` - 对数

**资源 (Resources):**
- `math://constants` - 数学常数

### 2. 社保查询服务 (social_security_server.py)

提供社保证明查询功能：

**工具 (Tools):**
- `query_social_certificate(token)` - 查询个人社保证明
- `query_social_equity(token, year)` - 查询社会保险个人权益信息表
- `validate_token(token)` - 验证认证令牌
- `get_service_status()` - 获取服务状态

**资源 (Resources):**
- `social://api-docs` - API文档信息
- `social://error-codes` - 错误代码说明

### 3. 四川省政策查询服务 (sichuan_policy_server.py)

提供四川省政策查询功能：

**工具 (Tools):**
- `query_sichuan_policy(query, area_code)` - 查询四川省相关政策
- `validate_area_code(area_code)` - 验证地区编码是否有效
- `get_policy_service_status()` - 获取政策查询服务状态

**资源 (Resources):**
- `policy://api-docs` - API文档信息
- `policy://area-codes` - 四川省地区编码表
- `policy://examples` - 政策查询常见问题示例

**支持的地区编码:**
- `5100000` - 四川省
- `5101000` - 成都市
- `5103000` - 自贡市
- `5104000` - 攀枝花市
- `5105000` - 泸州市
- 等其他四川省各市州编码

### 4. 办事指南查询服务 (service_guide_server.py)

提供四川省办事指南查询功能：

**工具 (Tools):**
- `search_service_events(query, area_code)` - 根据用户问题查询相关事项列表和ID
- `get_area_tree(area_level, parent_area_id)` - 查询地区信息树
- `search_event_details(query, event_id)` - 查询办事指南详情
- `validate_area_code(area_code)` - 验证地区编码是否有效
- `get_service_status()` - 获取办事指南服务状态

**资源 (Resources):**
- `service-guide://area-codes` - 常用地区编码
- `service-guide://question-templates` - 常见问题模板

**主要功能:**
- 事项搜索：根据关键词搜索相关办事事项
- 地区查询：支持省市县镇四级地区信息查询
- 详情查询：提供办理地点、时间、流程、材料等详细信息
- 智能问答：支持"在哪里办理？"、"需要什么材料？"等常见问题

**API接口:**
- 事项搜索：`http://*************:9873/knowledge_base/search_event`
- 地区查询：`https://************/gateway3/online-service/chatSessionRecord/getAreaTree`
- 详情查询：`http://*************:9873/knowledge_base/search_event_key`

## 使用方法

### 方式1：启动所有服务（推荐）

```bash
# 启动所有服务（默认端口8000）
python main_modular.py

# 指定端口和主机
python main_modular.py --port 9000 --host 127.0.0.1

# 启动指定服务
python main_modular.py --services calculator social policy service-guide

# 只启动计算器服务
python main_modular.py --services calculator

# 只启动政策查询服务
python main_modular.py --services policy

# 只启动办事指南服务
python main_modular.py --services service-guide
```


## 命令行参数

### main_modular.py 参数

- `--services`: 要启动的服务列表，可选值：`calculator`, `social`, `policy`, `service-guide`, `all`（默认：`all`）
- `--port`: 服务端口（默认：8000）
- `--host`: 服务主机地址（默认：0.0.0.0）

## API测试

### 测试计算器服务

```bash
# 加法
curl -X POST http://localhost:8000/tools/calculator/add \
  -H "Content-Type: application/json" \
  -d '{"a": 5, "b": 3}'

# 平方根
curl -X POST http://localhost:8000/tools/calculator/sqrt \
  -H "Content-Type: application/json" \
  -d '{"number": 16}'
```

### 测试社保查询服务

```bash
# 验证令牌
curl -X POST http://localhost:8000/tools/social/validate_token \
  -H "Content-Type: application/json" \
  -d '{"token": "your_token_here"}'

# 查询社保权益信息表
curl -X POST http://localhost:8000/tools/social/query_social_equity \
  -H "Content-Type: application/json" \
  -d '{"token": "your_token_here", "year": "2024"}'

# 获取服务状态
curl -X POST http://localhost:8000/tools/social/get_service_status \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 测试政策查询服务

```bash
# 查询四川省政策
curl -X POST http://localhost:8000/tools/policy/query_sichuan_policy \
  -H "Content-Type: application/json" \
  -d '{"query": "开办企业有什么政策", "area_code": "5100000"}'


# 获取政策服务状态
curl -X POST http://localhost:8000/tools/policy/get_policy_service_status \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 测试办事指南服务

```bash
# 搜索办事事项
curl -X POST http://localhost:8000/tools/service-guide/search_service_events \
  -H "Content-Type: application/json" \
  -d '{"query": "公共场所卫生许可", "area_code": "5100000"}'

# 查询地区信息
curl -X POST http://localhost:8000/tools/service-guide/get_area_tree \
  -H "Content-Type: application/json" \
  -d '{"area_level": "2", "parent_area_id": "1431"}'

# 查询办事指南详情（需要先从搜索结果中获取event_id）
curl -X POST http://localhost:8000/tools/service-guide/search_event_details \
  -H "Content-Type: application/json" \
  -d '{"query": "在哪里办理？", "event_id": "4602491079756374016"}'

# 验证地区编码
curl -X POST http://localhost:8000/tools/service-guide/validate_area_code \
  -H "Content-Type: application/json" \
  -d '{"area_code": "5100000"}'

# 获取办事指南服务状态
curl -X POST http://localhost:8000/tools/service-guide/get_service_status \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 测试组合功能

```bash
# 获取可用服务列表
curl -X POST http://localhost:8000/tools/get_available_services \
  -H "Content-Type: application/json" \
  -d '{}'

# 查询政策并计算相关数据
curl -X POST http://localhost:8000/tools/policy/query_sichuan_policy \
  -H "Content-Type: application/json" \
  -d '{"query": "创业贷款利率计算", "area_code": "5101000"}'
```

## 优势

1. **模块化设计**: 每个服务独立开发和维护
2. **灵活部署**: 可以选择启动全部或部分服务
3. **端口隔离**: 单独启动时使用不同端口避免冲突
4. **易于扩展**: 添加新服务只需创建新模块并在主程序中导入
5. **统一管理**: 主程序提供统一的服务管理和组合功能

## 快速开始

### 1. 激活虚拟环境
```bash
.venv\Scripts\activate
```

### 2. 启动服务

**启动所有服务（推荐）:**
```bash
python main_modular.py
# 访问: http://localhost:8000/mcp/
```

**启动指定服务:**
```bash
python main_modular.py --services calculator
python main_modular.py --services social
```

**单独启动服务:**
```bash
python run_calculator.py --port 8001
python run_social_security.py --port 8002
```

## 服务状态验证

当服务启动后，你会看到类似以下的输出：
```
启动模块化MCP服务器
地址: http://0.0.0.0:8000
启动的服务: calculator, social
```

## 扩展新服务

要添加新的MCP服务：

1. 在 `modules/` 目录下创建新的服务文件
2. 使用 `FastMCP` 创建服务实例
3. 在 `main_modular.py` 中导入并注册新服务
4. 创建对应的单独启动脚本（可选）

示例：
```python
# modules/new_service.py
from fastmcp import FastMCP

new_service_mcp = FastMCP(name="NewService")

@new_service_mcp.tool()
def new_function():
    return "Hello from new service"
```

## 政策查询服务使用示例

### 常见查询场景

```python
# 查询创业政策
query_sichuan_policy("开办企业有什么政策", "5101000")  # 成都市

# 查询民营经济政策
query_sichuan_policy("民营经济发展有哪些支持措施", "5100000")  # 四川省

# 查询个体工商户政策
query_sichuan_policy("个体工商户有什么优惠政策", "5101000")  # 成都市

# 查询残疾人创业政策
query_sichuan_policy("残疾人创业有什么补贴", "5100000")  # 四川省
```

## 办事指南服务使用示例

### 完整查询流程

```python
# 1. 搜索相关事项
result = search_service_events("公共场所卫生许可", "5100000")
event_list = result.get("event_data_list", [])

# 2. 选择具体事项
if event_list:
    event_id = event_list[0]["event_id"]
    event_name = event_list[0]["event_name"]
    print(f"选择事项: {event_name}")

    # 3. 查询详细信息
    questions = [
        "在哪里办理？",
        "需要什么材料？",
        "办理流程是什么？",
        "办理时间是多久？"
    ]

    for question in questions:
        detail = search_event_details(question, event_id)
        print(f"问题: {question}")
        print(f"回答: {detail.get('output_text', '暂无信息')}")
```

### 地区查询示例

```python
# 查询四川省下的市级地区
cities = get_area_tree("2", "1431")

# 查询成都市下的区县
districts = get_area_tree("3", "成都市的area_id")
```

### 演示脚本

项目提供了两个演示脚本：

```bash
# 运行完整流程演示
python demo_service_guide.py

# 运行功能测试
python test_service_guide.py
```

## 注意事项

1. **环境要求**: 需要激活uv虚拟环境
2. **端口冲突**: 确保指定的端口未被占用
3. **服务依赖**:
   - 社保查询服务需要网络访问权限
   - 政策查询服务需要访问内网API (http://10.206.23.238)
   - 办事指南服务需要访问内网API (http://*************:9873 和 https://************)
4. **日志级别**: 可以通过修改logging配置调整日志输出
5. **地区编码**: 政策查询和办事指南服务目前支持四川省及其各市州的地区编码
6. **办事指南服务**:
   - 支持事项搜索、地区查询和详情查询三大功能
   - 提供常见问题模板和地区编码资源
   - 包含完整的错误处理和服务状态检查
