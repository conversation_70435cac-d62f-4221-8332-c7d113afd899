from fastmcp import FastMCP
import requests
import logging
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建社保查询MCP服务
social_security_mcp = FastMCP(name="SocialSecurityService")

@social_security_mcp.tool()
def query_social_certificate(token: str) -> Dict[str, Any]:
    """
    查询个人社保证明

    Args:
        token: 认证令牌

    Returns:
        包含预览URL和下载URL的结果
    """
    url = "https://10.206.23.71/gateway3/online-service/chatApi/socialCertificate"

    # 设置请求头
    headers = {
        "Authorization": token,
        "Content-Type": "application/json"
    }

    try:
        # 注意：正式环境中应验证SSL证书，这里因为是内网地址，可能需要禁用证书验证
        response = requests.post(url, headers=headers, json={}, verify=False)

        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            logging.error(f"API请求失败，状态码: {response.status_code}")
            return {"code": "error", "msg": f"API请求失败，状态码: {response.status_code}"}

    except Exception as e:
        logging.error(f"请求过程中发生错误: {str(e)}")
        return {"code": "error", "msg": f"请求过程中发生错误: {str(e)}"}

@social_security_mcp.tool()
def query_social_equity(token: str, year: str = "2024") -> Dict[str, Any]:
    """
    查询社会保险个人权益信息表

    Args:
        token: 认证令牌
        year: 查询年份（默认为2024）

    Returns:
        包含预览URL和下载URL的结果
    """
    url = "https://10.206.23.71/gateway3/online-service/chatApi/socialEquity"

    # 设置请求头
    headers = {
        "Authorization": token,
        "Content-Type": "application/json"
    }

    # 设置请求参数
    payload = {"year": year}

    try:
        # 注意：正式环境中应验证SSL证书，这里因为是内网地址，可能需要禁用证书验证
        response = requests.post(url, headers=headers, json=payload, verify=False)

        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            logging.error(f"社保权益查询API请求失败，状态码: {response.status_code}")
            return {"code": "error", "msg": f"社保权益查询API请求失败，状态码: {response.status_code}"}

    except Exception as e:
        logging.error(f"社保权益查询请求过程中发生错误: {str(e)}")
        return {"code": "error", "msg": f"社保权益查询请求过程中发生错误: {str(e)}"}


@social_security_mcp.tool()
def get_service_status() -> Dict[str, Any]:
    """
    获取社保服务状态
    
    Returns:
        服务状态信息
    """
    try:
        # 简单的健康检查
        test_url = "https://10.206.23.71/gateway3/online-service/health"
        response = requests.get(test_url, timeout=5, verify=False)
        
        if response.status_code == 200:
            return {"status": "online", "msg": "服务正常"}
        else:
            return {"status": "error", "msg": f"服务异常，状态码: {response.status_code}"}
    except Exception as e:
        return {"status": "offline", "msg": f"服务不可用: {str(e)}"}


# 资源：错误代码说明
@social_security_mcp.resource("social://error-codes")
def get_error_codes() -> Dict[str, str]:
    """提供错误代码说明"""
    return {
        "AUTH_FAILED": "认证失败，请检查令牌",
        "INVALID_TOKEN": "无效的令牌格式",
        "SERVICE_UNAVAILABLE": "服务暂时不可用",
        "NETWORK_ERROR": "网络连接错误",
        "TIMEOUT": "请求超时"
    }
