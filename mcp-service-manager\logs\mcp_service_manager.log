2025-07-29 13:41:25,117 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 13:41:25,118 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 13:41:25,118 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 13:41:25,201 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 13:41:25,202 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 13:41:25,202 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 13:41:25,208 - src.mcp_service_manager.config_manager - INFO - 配置文件不存在，创建新的配置
2025-07-29 13:41:25,209 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 13:41:25,209 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 13:41:25,209 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 13:42:53,350 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 13:42:53,350 - src.mcp_service_manager.config_manager - INFO - 已添加服务: 测试API服务 (ID: 00aa841a-4006-403c-9448-918d564f0e5f, Port: 8001)
2025-07-29 13:42:53,365 - src.mcp_service_manager.service_generator - ERROR - 启动服务失败: name 'json' is not defined
2025-07-29 13:42:53,365 - src.mcp_service_manager.api.routes - ERROR - 启动服务API错误: 
2025-07-29 13:42:53,377 - src.mcp_service_manager.service_generator - WARNING - 服务未运行: 00aa841a-4006-403c-9448-918d564f0e5f
2025-07-29 13:42:53,378 - src.mcp_service_manager.api.routes - ERROR - 停止服务API错误: 
2025-07-29 13:42:53,384 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 13:42:53,385 - src.mcp_service_manager.config_manager - INFO - 已删除服务: 测试API服务 (ID: 00aa841a-4006-403c-9448-918d564f0e5f)
2025-07-29 13:44:20,859 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 13:44:20,860 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 13:44:20,860 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 13:44:20,917 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 13:44:20,918 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 13:44:20,918 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 13:44:20,919 - src.mcp_service_manager.config_manager - INFO - 已加载 0 个服务配置
2025-07-29 13:44:20,920 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 13:44:20,920 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 13:44:37,504 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 13:44:37,504 - src.mcp_service_manager.config_manager - INFO - 已添加服务: 测试API服务 (ID: 04cec0b7-7ce1-498a-86f9-824598a65714, Port: 8001)
2025-07-29 13:44:37,518 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\04cec0b7-7ce1-498a-86f9-824598a65714.py
2025-07-29 13:44:37,518 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: D:\Anaconda3\python.exe configs\services\04cec0b7-7ce1-498a-86f9-824598a65714.py --port 8001
2025-07-29 13:44:37,521 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 测试API服务 (PID: 30984, Port: 8001)
2025-07-29 13:44:37,522 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 13:44:37,522 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 04cec0b7-7ce1-498a-86f9-824598a65714
2025-07-29 13:44:43,623 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 04cec0b7-7ce1-498a-86f9-824598a65714
2025-07-29 13:44:43,624 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 13:44:43,624 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 04cec0b7-7ce1-498a-86f9-824598a65714
2025-07-29 13:44:43,627 - src.mcp_service_manager.service_generator - INFO - 已删除服务文件: configs\services\04cec0b7-7ce1-498a-86f9-824598a65714.py
2025-07-29 13:44:43,628 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 13:44:43,628 - src.mcp_service_manager.config_manager - INFO - 已删除服务: 测试API服务 (ID: 04cec0b7-7ce1-498a-86f9-824598a65714)
2025-07-29 13:51:46,015 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 13:51:46,016 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 13:51:46,016 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 13:51:46,017 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 13:51:46,018 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 13:51:46,018 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 13:51:46,018 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 13:52:23,117 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 13:52:23,117 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 13:52:23,118 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 13:52:23,170 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 13:52:23,171 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 13:52:23,171 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 13:52:23,171 - src.mcp_service_manager.config_manager - INFO - 已加载 0 个服务配置
2025-07-29 13:52:23,172 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 13:52:23,172 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 13:59:17,331 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 13:59:17,331 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 13:59:17,331 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 13:59:17,375 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 13:59:17,375 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 13:59:17,375 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 13:59:17,376 - src.mcp_service_manager.config_manager - INFO - 已加载 0 个服务配置
2025-07-29 13:59:17,376 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 13:59:17,376 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 13:59:17,378 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 13:59:17,379 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 13:59:17,379 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 13:59:17,379 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 14:04:36,712 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 14:04:36,712 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:04:36,712 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:04:36,713 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 14:04:36,713 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:04:36,713 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:04:36,713 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 14:07:47,084 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 14:07:47,084 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 14:07:47,084 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 14:07:47,117 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 14:07:47,118 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 14:07:47,118 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 14:07:47,119 - src.mcp_service_manager.config_manager - INFO - 已加载 0 个服务配置
2025-07-29 14:07:47,119 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 14:07:47,119 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 14:11:29,395 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:11:29,396 - src.mcp_service_manager.config_manager - INFO - 已添加服务: 四川省政策查询服务 (ID: 441a3444-801c-4faf-811d-ec3d1bb7438c, Port: 15200)
2025-07-29 14:13:17,752 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py
2025-07-29 14:13:17,753 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: D:\Anaconda3\python.exe configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py --port 15200
2025-07-29 14:13:17,755 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 25660, Port: 15200)
2025-07-29 14:13:17,757 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:13:17,758 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:20:31,774 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 14:20:31,774 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:20:31,774 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:20:31,775 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:20:31,775 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 14:20:31,775 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:20:31,776 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:20:31,776 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 14:24:54,570 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 14:24:54,570 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 14:24:54,570 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 14:24:54,590 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 14:24:54,590 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 14:24:54,591 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 14:24:54,591 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 14:24:54,591 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 14:24:54,592 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 14:25:05,746 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py
2025-07-29 14:25:05,747 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py --port 15200
2025-07-29 14:25:05,751 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 27140, Port: 15200)
2025-07-29 14:25:05,752 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:25:05,752 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:25:13,900 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:25:13,902 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:25:13,903 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:25:32,619 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py
2025-07-29 14:25:32,619 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py --port 15200
2025-07-29 14:25:32,621 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 15420, Port: 15200)
2025-07-29 14:25:32,621 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:25:32,622 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:29:36,994 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 14:29:36,994 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 14:29:36,995 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 14:29:37,027 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 14:29:37,028 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 14:29:37,028 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 14:29:37,028 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 14:29:37,029 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 14:29:37,029 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 14:29:37,029 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 14:29:37,029 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:29:37,030 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:29:37,030 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 14:30:04,359 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 14:30:04,360 - __main__ - INFO - 管理界面: http://0.0.0.0:8001
2025-07-29 14:30:04,360 - __main__ - INFO - API文档: http://0.0.0.0:8001/docs
2025-07-29 14:30:04,394 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 14:30:04,395 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 14:30:04,395 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 14:30:04,396 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 14:30:04,396 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 14:30:04,396 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 14:31:00,154 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py
2025-07-29 14:31:00,154 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: D:\Anaconda3\python.exe configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py --port 15200
2025-07-29 14:31:00,156 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 19756, Port: 15200)
2025-07-29 14:31:00,157 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:31:00,158 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:31:33,823 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:31:33,826 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:31:33,827 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:31:36,519 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py
2025-07-29 14:31:36,519 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: D:\Anaconda3\python.exe configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py --port 15200
2025-07-29 14:31:36,521 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 5016, Port: 15200)
2025-07-29 14:31:36,522 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:31:36,522 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:39:01,411 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 14:39:01,411 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:39:01,412 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:39:01,412 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:39:01,413 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 14:39:01,413 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:39:01,413 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:39:01,413 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 14:39:44,525 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 14:39:44,526 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 14:39:44,526 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 14:39:44,546 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 14:39:44,548 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 14:39:44,548 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 14:39:44,549 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 14:39:44,550 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 14:39:44,550 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 14:40:07,461 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py
2025-07-29 14:40:07,462 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py --port 15200
2025-07-29 14:40:07,467 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 27164, Port: 15200)
2025-07-29 14:40:07,469 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:40:07,470 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:46:07,224 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 14:46:07,224 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:46:07,225 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:46:07,225 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:46:07,226 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 14:46:07,226 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:46:07,227 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:46:07,227 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 14:46:08,958 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 14:46:08,958 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 14:46:08,958 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 14:46:08,977 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 14:46:08,978 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 14:46:08,978 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 14:46:08,979 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 14:46:08,979 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 14:46:08,979 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 14:50:19,267 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 14:50:19,267 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:50:19,268 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:50:19,268 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 14:50:19,268 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:50:19,268 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:50:19,269 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 14:50:31,646 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 14:50:31,646 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 14:50:31,646 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 14:50:31,665 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 14:50:31,665 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 14:50:31,666 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 14:50:31,666 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 14:50:31,667 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 14:50:31,667 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 14:51:44,665 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py
2025-07-29 14:51:44,666 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py --port 15200
2025-07-29 14:51:44,671 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 6808, Port: 15200)
2025-07-29 14:51:44,673 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:51:44,674 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:54:07,748 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 14:54:07,748 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:54:07,749 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:54:07,749 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:54:07,750 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 14:54:07,750 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:54:07,750 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:54:07,750 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 14:54:50,935 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 14:54:50,936 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 14:54:50,936 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 14:54:50,956 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 14:54:50,956 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 14:54:50,957 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 14:54:50,957 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 14:54:50,957 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 14:54:50,958 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 14:54:56,262 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py
2025-07-29 14:54:56,263 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py --port 15200
2025-07-29 14:54:56,266 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 8736, Port: 15200)
2025-07-29 14:54:56,266 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:54:56,266 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:56:52,872 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 14:56:52,872 - __main__ - INFO - 管理界面: http://0.0.0.0:8001
2025-07-29 14:56:52,872 - __main__ - INFO - API文档: http://0.0.0.0:8001/docs
2025-07-29 14:56:52,919 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 14:56:52,919 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 14:56:52,920 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 14:56:52,920 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 14:56:52,920 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 14:56:52,921 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 14:57:12,518 - src.mcp_service_manager.service_generator - INFO - 已删除服务文件: configs\services\441a3444-801c-4faf-811d-ec3d1bb7438c.py
2025-07-29 14:57:12,518 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 14:57:12,519 - src.mcp_service_manager.config_manager - INFO - 已删除服务: 四川省政策查询服务 (ID: 441a3444-801c-4faf-811d-ec3d1bb7438c)
2025-07-29 14:58:55,979 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 14:58:55,982 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:58:55,985 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 441a3444-801c-4faf-811d-ec3d1bb7438c
2025-07-29 14:58:55,986 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:58:55,987 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 14:58:55,987 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 14:58:55,988 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 14:58:55,988 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 15:00:21,054 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 15:00:21,055 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 15:00:21,055 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 15:00:21,055 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 15:00:21,055 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 15:00:21,056 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 15:00:21,056 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 15:00:33,817 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 15:00:33,817 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 15:00:33,817 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 15:00:33,836 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 15:00:33,836 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 15:00:33,837 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 15:00:33,837 - src.mcp_service_manager.config_manager - INFO - 已加载 0 个服务配置
2025-07-29 15:00:33,837 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 15:00:33,837 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 15:02:31,364 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:02:31,365 - src.mcp_service_manager.config_manager - INFO - 已添加服务: 四川省政策查询服务 (ID: d5fbe592-1582-4faa-a7bc-f1b9cf20d532, Port: 15235)
2025-07-29 15:02:36,278 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\d5fbe592-1582-4faa-a7bc-f1b9cf20d532.py
2025-07-29 15:02:36,279 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\d5fbe592-1582-4faa-a7bc-f1b9cf20d532.py --port 15235
2025-07-29 15:02:36,281 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 27328, Port: 15235)
2025-07-29 15:02:36,281 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:02:36,282 - src.mcp_service_manager.config_manager - INFO - 已更新服务: d5fbe592-1582-4faa-a7bc-f1b9cf20d532
2025-07-29 15:06:27,479 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 15:06:27,480 - __main__ - INFO - 管理界面: http://0.0.0.0:8001
2025-07-29 15:06:27,480 - __main__ - INFO - API文档: http://0.0.0.0:8001/docs
2025-07-29 15:06:27,515 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 15:06:27,516 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 15:06:27,516 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 15:06:27,517 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 15:06:27,517 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 15:06:27,517 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 15:09:14,659 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 15:09:14,660 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 15:09:14,661 - src.mcp_service_manager.service_generator - INFO - 服务已停止: d5fbe592-1582-4faa-a7bc-f1b9cf20d532
2025-07-29 15:09:14,661 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 15:09:14,662 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 15:09:14,662 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 15:09:14,663 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 15:09:14,663 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 15:14:50,567 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 15:14:50,567 - __main__ - INFO - 管理界面: http://0.0.0.0:8001
2025-07-29 15:14:50,567 - __main__ - INFO - API文档: http://0.0.0.0:8001/docs
2025-07-29 15:14:50,601 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 15:14:50,601 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 15:14:50,602 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 15:14:50,602 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 15:14:50,603 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 15:14:50,603 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 15:15:31,633 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 15:15:31,633 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 15:15:31,633 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 15:15:31,652 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 15:15:31,653 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 15:15:31,653 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 15:15:31,654 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 15:15:31,654 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 15:15:31,655 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 15:17:08,034 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:17:08,034 - src.mcp_service_manager.config_manager - INFO - 已添加服务: 四川省政策查询服务 (ID: 2c19e198-e0ba-4a8c-a245-a51b7becfd5d, Port: 15235)
2025-07-29 15:17:42,987 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:17:42,988 - src.mcp_service_manager.config_manager - INFO - 已删除服务: 四川省政策查询服务 (ID: 2c19e198-e0ba-4a8c-a245-a51b7becfd5d)
2025-07-29 15:17:43,990 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\d5fbe592-1582-4faa-a7bc-f1b9cf20d532.py
2025-07-29 15:17:43,990 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\d5fbe592-1582-4faa-a7bc-f1b9cf20d532.py --port 15235
2025-07-29 15:17:43,992 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 27596, Port: 15235)
2025-07-29 15:17:43,992 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:17:43,993 - src.mcp_service_manager.config_manager - INFO - 已更新服务: d5fbe592-1582-4faa-a7bc-f1b9cf20d532
2025-07-29 15:30:50,513 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 15:30:50,514 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 15:30:50,514 - src.mcp_service_manager.service_generator - INFO - 服务已停止: d5fbe592-1582-4faa-a7bc-f1b9cf20d532
2025-07-29 15:30:50,515 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 15:30:50,516 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 15:30:50,516 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 15:30:50,516 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 15:30:50,517 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 15:30:52,364 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 15:30:52,365 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 15:30:52,365 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 15:30:52,384 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 15:30:52,385 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 15:30:52,385 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 15:30:52,386 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 15:30:52,386 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 15:30:52,386 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 15:31:02,545 - src.mcp_service_manager.service_generator - INFO - 已删除服务文件: configs\services\d5fbe592-1582-4faa-a7bc-f1b9cf20d532.py
2025-07-29 15:31:02,546 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:31:02,547 - src.mcp_service_manager.config_manager - INFO - 已删除服务: 四川省政策查询服务 (ID: d5fbe592-1582-4faa-a7bc-f1b9cf20d532)
2025-07-29 15:32:45,147 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:32:45,147 - src.mcp_service_manager.config_manager - INFO - 已添加服务: 四川省政策查询服务 (ID: 28930cb9-7ac5-48cf-bc5c-b1a168baecda, Port: 15235)
2025-07-29 15:32:47,249 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\28930cb9-7ac5-48cf-bc5c-b1a168baecda.py
2025-07-29 15:32:47,249 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\28930cb9-7ac5-48cf-bc5c-b1a168baecda.py --port 15235
2025-07-29 15:32:47,252 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 25200, Port: 15235)
2025-07-29 15:32:47,253 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:32:47,253 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 28930cb9-7ac5-48cf-bc5c-b1a168baecda
2025-07-29 15:39:00,656 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 15:39:00,657 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 15:39:00,658 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 28930cb9-7ac5-48cf-bc5c-b1a168baecda
2025-07-29 15:39:00,658 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 15:39:00,659 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 15:39:00,659 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 15:39:00,659 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 15:39:00,660 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-07-29 15:50:19,251 - __main__ - INFO - 启动MCP服务管理器
2025-07-29 15:50:19,251 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-07-29 15:50:19,251 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-07-29 15:50:19,270 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-07-29 15:50:19,271 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-07-29 15:50:19,271 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-07-29 15:50:19,272 - src.mcp_service_manager.config_manager - INFO - 已加载 1 个服务配置
2025-07-29 15:50:19,272 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-07-29 15:50:19,272 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-07-29 15:50:21,974 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:50:21,974 - src.mcp_service_manager.config_manager - INFO - 已添加服务: 查询地区 (ID: 2699649c-0fe2-4d08-830e-063a2b2606c0, Port: 15238)
2025-07-29 15:50:31,486 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\2699649c-0fe2-4d08-830e-063a2b2606c0.py
2025-07-29 15:50:31,487 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\2699649c-0fe2-4d08-830e-063a2b2606c0.py --port 15238
2025-07-29 15:50:31,491 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 查询地区 (PID: 1616, Port: 15238)
2025-07-29 15:50:31,492 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:50:31,493 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-07-29 15:58:58,384 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\28930cb9-7ac5-48cf-bc5c-b1a168baecda.py
2025-07-29 15:58:58,385 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\28930cb9-7ac5-48cf-bc5c-b1a168baecda.py --port 15235
2025-07-29 15:58:58,390 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 四川省政策查询服务 (PID: 30288, Port: 15235)
2025-07-29 15:58:58,392 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:58:58,393 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 28930cb9-7ac5-48cf-bc5c-b1a168baecda
2025-07-29 15:59:44,167 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-07-29 15:59:44,169 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:59:44,169 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-07-29 15:59:44,952 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\2699649c-0fe2-4d08-830e-063a2b2606c0.py
2025-07-29 15:59:44,952 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\2699649c-0fe2-4d08-830e-063a2b2606c0.py --port 15238
2025-07-29 15:59:44,954 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 查询地区 (PID: 28036, Port: 15238)
2025-07-29 15:59:44,955 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-07-29 15:59:44,955 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-07-29 17:00:05,557 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-07-29 17:00:05,558 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 17:00:05,560 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 28930cb9-7ac5-48cf-bc5c-b1a168baecda
2025-07-29 17:00:05,561 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-07-29 17:00:05,561 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 17:00:05,562 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-07-29 17:00:05,562 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-07-29 17:00:05,562 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-07-29 17:00:05,562 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
2025-09-16 13:32:20,749 - __main__ - INFO - 启动MCP服务管理器
2025-09-16 13:32:20,749 - __main__ - INFO - 管理界面: http://0.0.0.0:8000
2025-09-16 13:32:20,749 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-09-16 13:32:20,773 - src.mcp_service_manager.main - INFO - MCP服务管理器启动中...
2025-09-16 13:32:20,774 - src.mcp_service_manager.main - INFO - 静态文件目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\static
2025-09-16 13:32:20,774 - src.mcp_service_manager.main - INFO - 模板目录: E:\work\code\fastmcp\mcp-service-manager\src\mcp_service_manager\web\templates
2025-09-16 13:32:20,775 - src.mcp_service_manager.config_manager - INFO - 已加载 2 个服务配置
2025-09-16 13:32:20,775 - src.mcp_service_manager.main - INFO - 服务管理器初始化完成
2025-09-16 13:32:20,775 - src.mcp_service_manager.main - INFO - MCP服务管理器启动完成
2025-09-16 13:33:34,996 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\2699649c-0fe2-4d08-830e-063a2b2606c0.py
2025-09-16 13:33:34,997 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\2699649c-0fe2-4d08-830e-063a2b2606c0.py --port 15238
2025-09-16 13:33:35,003 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 查询地区 (PID: 10088, Port: 15238)
2025-09-16 13:33:35,005 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-09-16 13:33:35,006 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-09-16 13:33:38,833 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-09-16 13:33:38,834 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-09-16 13:33:38,834 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-09-16 13:35:25,332 - src.mcp_service_manager.service_generator - INFO - 已创建服务文件: configs\services\2699649c-0fe2-4d08-830e-063a2b2606c0.py
2025-09-16 13:35:25,333 - src.mcp_service_manager.service_generator - INFO - 启动服务命令: E:\work\code\fastmcp\mcp-service-manager\.venv\Scripts\python.exe configs\services\2699649c-0fe2-4d08-830e-063a2b2606c0.py --port 15238
2025-09-16 13:35:25,338 - src.mcp_service_manager.service_generator - INFO - 服务已启动: 查询地区 (PID: 15288, Port: 15238)
2025-09-16 13:35:25,339 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-09-16 13:35:25,340 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-09-16 13:35:31,800 - src.mcp_service_manager.service_generator - INFO - 服务已停止: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-09-16 13:35:31,802 - src.mcp_service_manager.config_manager - INFO - 配置已保存
2025-09-16 13:35:31,802 - src.mcp_service_manager.config_manager - INFO - 已更新服务: 2699649c-0fe2-4d08-830e-063a2b2606c0
2025-09-16 13:38:25,796 - src.mcp_service_manager.main - INFO - 接收到信号 2，正在关闭...
2025-09-16 13:38:25,797 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-09-16 13:38:25,797 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-09-16 13:38:25,803 - src.mcp_service_manager.main - INFO - MCP服务管理器关闭中...
2025-09-16 13:38:25,803 - src.mcp_service_manager.service_manager - INFO - 正在关闭服务管理器...
2025-09-16 13:38:25,803 - src.mcp_service_manager.service_manager - INFO - 所有服务已停止
2025-09-16 13:38:25,804 - src.mcp_service_manager.main - INFO - MCP服务管理器已关闭
