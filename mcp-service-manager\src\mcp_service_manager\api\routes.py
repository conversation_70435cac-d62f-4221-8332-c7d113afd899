from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 全局服务管理器实例（将在main.py中设置）
service_manager = None

def set_service_manager(manager):
    """设置服务管理器实例"""
    global service_manager
    service_manager = manager

# Pydantic模型
class APIConfigModel(BaseModel):
    url: str
    method: str = "POST"
    headers: Dict[str, str] = {"Content-Type": "application/json"}
    params: Dict[str, Any] = {}
    timeout: int = 30

class CreateServiceModel(BaseModel):
    name: str
    description: str
    api_config: APIConfigModel
    port: Optional[int] = None
    tool_description: Optional[str] = None

class UpdateServiceModel(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    api_config: Optional[APIConfigModel] = None
    port: Optional[int] = None
    tool_description: Optional[str] = None

# API路由
@router.post("/api/services")
async def create_service(service_data: CreateServiceModel):
    """创建新的MCP服务"""
    if not service_manager:
        raise HTTPException(status_code=500, detail="服务管理器未初始化")
    
    try:
        result = service_manager.create_service(
            name=service_data.name,
            description=service_data.description,
            api_config_dict=service_data.api_config.dict(),
            port=service_data.port,
            tool_description=service_data.tool_description
        )
        
        if result["success"]:
            return JSONResponse(content=result, status_code=201)
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "创建服务失败"))
    except Exception as e:
        logger.error(f"创建服务API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/services")
async def list_services():
    """获取所有服务列表"""
    if not service_manager:
        raise HTTPException(status_code=500, detail="服务管理器未初始化")
    
    try:
        result = service_manager.list_services()
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("error", "获取服务列表失败"))
    except Exception as e:
        logger.error(f"获取服务列表API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/services/{service_id}")
async def get_service(service_id: str):
    """获取特定服务的详细信息"""
    if not service_manager:
        raise HTTPException(status_code=500, detail="服务管理器未初始化")
    
    try:
        result = service_manager.get_service_info(service_id)
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=404, detail=result.get("error", "服务不存在"))
    except Exception as e:
        logger.error(f"获取服务信息API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/services/{service_id}/start")
async def start_service(service_id: str):
    """启动MCP服务"""
    if not service_manager:
        raise HTTPException(status_code=500, detail="服务管理器未初始化")
    
    try:
        result = service_manager.start_service(service_id)
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "启动服务失败"))
    except Exception as e:
        logger.error(f"启动服务API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/services/{service_id}/stop")
async def stop_service(service_id: str):
    """停止MCP服务"""
    if not service_manager:
        raise HTTPException(status_code=500, detail="服务管理器未初始化")
    
    try:
        result = service_manager.stop_service(service_id)
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "停止服务失败"))
    except Exception as e:
        logger.error(f"停止服务API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/api/services/{service_id}")
async def delete_service(service_id: str):
    """删除MCP服务"""
    if not service_manager:
        raise HTTPException(status_code=500, detail="服务管理器未初始化")
    
    try:
        result = service_manager.delete_service(service_id)
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "删除服务失败"))
    except Exception as e:
        logger.error(f"删除服务API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/api/services/{service_id}")
async def update_service(service_id: str, service_data: UpdateServiceModel):
    """更新服务配置"""
    if not service_manager:
        raise HTTPException(status_code=500, detail="服务管理器未初始化")
    
    try:
        # 构建更新参数
        update_params = {}
        if service_data.name is not None:
            update_params["name"] = service_data.name
        if service_data.description is not None:
            update_params["description"] = service_data.description
        if service_data.port is not None:
            update_params["port"] = service_data.port
        if service_data.api_config is not None:
            update_params["api_config"] = service_data.api_config.dict()
        
        result = service_manager.update_service_config(service_id, **update_params)
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "更新服务失败"))
    except Exception as e:
        logger.error(f"更新服务API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/available-port")
async def get_available_port():
    """获取可用端口"""
    if not service_manager:
        raise HTTPException(status_code=500, detail="服务管理器未初始化")
    
    try:
        port = service_manager.get_available_port()
        return {"port": port}
    except Exception as e:
        logger.error(f"获取可用端口API错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "message": "MCP服务管理器运行正常"}
