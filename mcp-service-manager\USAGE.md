# MCP服务管理器使用指南

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境
.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac

# 安装依赖（使用清华源）
uv add "mcp[cli]" --index-url https://pypi.tuna.tsinghua.edu.cn/simple
uv add fastmcp --index-url https://pypi.tuna.tsinghua.edu.cn/simple
```

### 2. 启动服务

```bash
python -m src.mcp_service_manager.main
```

服务启动后，访问：
- **管理界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 使用示例

### 示例1：创建一个简单的API服务

假设你有一个后端API：`http://api.example.com/weather`

1. 在Web界面中填写：
   - **服务名称**: 天气查询服务
   - **服务描述**: 查询指定城市的天气信息
   - **API URL**: http://api.example.com/weather
   - **HTTP方法**: POST
   - **请求头**:
     ```json
     {
       "Content-Type": "application/json",
       "Authorization": "Bearer your-token"
     }
     ```
   - **默认参数**:
     ```json
     {
       "format": "json"
     }
     ```

2. 点击"创建服务"
3. 在服务列表中点击"启动"按钮
4. 服务将在指定端口启动（如8001）

### 示例2：使用社保查询API

基于你现有的社保查询需求：

1. **服务配置**:
   - **服务名称**: 社保证明查询
   - **API URL**: https://************/gateway3/online-service/chatApi/socialCertificate
   - **请求头**:
     ```json
     {
       "Content-Type": "application/json"
     }
     ```

2. **调用方式**:
   启动后，MCP客户端可以连接到 `http://localhost:8001` 并调用工具，传入参数：
   ```json
   {
     "token": "your-auth-token"
   }
   ```

### 示例3：政策查询服务

1. **服务配置**:
   - **服务名称**: 四川省政策查询
   - **API URL**: http://*************/v1/workflows/run
   - **请求头**:
     ```json
     {
       "Content-Type": "application/json"
     }
     ```
   - **默认参数**:
     ```json
     {
       "response_mode": "blocking"
     }
     ```

## API接口说明

### 创建服务
```http
POST /api/services
Content-Type: application/json

{
  "name": "服务名称",
  "description": "服务描述",
  "api_config": {
    "url": "http://api.example.com/endpoint",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json"
    },
    "params": {
      "default_param": "value"
    },
    "timeout": 30
  },
  "port": 8001
}
```

### 启动服务
```http
POST /api/services/{service_id}/start
```

### 停止服务
```http
POST /api/services/{service_id}/stop
```

### 删除服务
```http
DELETE /api/services/{service_id}
```

### 获取服务列表
```http
GET /api/services
```

## 生成的MCP服务特性

每个动态生成的MCP服务包含：

1. **主要工具函数**: 根据服务名称生成，调用配置的后端API
2. **服务信息工具**: `get_service_info()` - 获取服务基本信息
3. **错误处理**: 自动处理网络错误、超时、JSON解析等
4. **日志记录**: 详细的请求和响应日志
5. **参数合并**: 支持动态参数与默认参数合并

## 注意事项

1. **端口管理**: 系统会自动分配可用端口，避免冲突
2. **SSL证书**: 内网API默认禁用SSL验证，生产环境请启用
3. **参数格式**: 请求头和参数必须是有效的JSON格式
4. **服务状态**: 服务停止后需要手动重启
5. **日志查看**: 服务日志保存在 `logs/` 目录下

## 故障排除

### 服务启动失败
- 检查端口是否被占用
- 确认API URL格式正确
- 查看日志文件获取详细错误信息

### API调用失败
- 验证请求头和参数格式
- 检查网络连接
- 确认API端点可访问

### 前端界面无法访问
- 确认服务已启动
- 检查防火墙设置
- 验证端口8000是否可用

## 扩展功能

系统支持以下扩展：

1. **自定义端口**: 在创建服务时指定端口
2. **多种HTTP方法**: 支持GET、POST、PUT、DELETE
3. **灵活参数**: 支持动态参数传递
4. **服务监控**: 实时查看服务运行状态
5. **配置持久化**: 服务配置自动保存到文件
