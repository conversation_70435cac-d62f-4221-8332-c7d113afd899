# MCP服务器配置文件
# 此文件用于配置MCP服务器的启动参数
# 配置项会被启动脚本自动加载

# ==================== 基本配置 ====================

# 服务列表 (可选值: social,policy,service-guide,all)
# 多个服务用逗号分隔，例如: social,policy
# 使用 "all" 启动所有可用服务
MCP_SERVICES="all"

# 服务端口
MCP_PORT=8000

# 服务主机地址
# 0.0.0.0 表示监听所有网络接口
# 127.0.0.1 表示只监听本地回环接口
MCP_HOST="0.0.0.0"

# 是否以后台模式运行 (true/false)
MCP_DAEMON="false"

# ==================== 日志配置 ====================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL="INFO"

# 日志文件路径 (相对于脚本目录)
LOG_FILE="mcp_server.log"

# 是否启用日志轮转
LOG_ROTATION="true"

# 日志文件最大大小 (MB)
LOG_MAX_SIZE=100

# 保留的日志文件数量
LOG_BACKUP_COUNT=5

# ==================== 性能配置 ====================

# 工作进程数 (0表示自动检测CPU核心数)
WORKERS=0

# 每个工作进程的最大连接数
MAX_CONNECTIONS=1000

# 请求超时时间 (秒)
REQUEST_TIMEOUT=30

# ==================== 安全配置 ====================

# 是否启用CORS (true/false)
ENABLE_CORS="true"

# 允许的源域名 (多个用逗号分隔，* 表示允许所有)
CORS_ORIGINS="*"

# 是否启用API密钥验证 (true/false)
ENABLE_API_KEY="false"

# API密钥 (当启用API密钥验证时使用)
API_KEY=""

# ==================== 服务特定配置 ====================

# 社保查询服务配置
SOCIAL_API_BASE_URL="https://10.206.23.71/gateway3/online-service"
SOCIAL_TIMEOUT=30

# 政策查询服务配置
POLICY_API_BASE_URL="http://10.206.23.238/v1/workflows/run"
POLICY_TIMEOUT=30

# 办事指南服务配置
SERVICE_GUIDE_API_BASE_URL="http://10.206.23.123:9873/knowledge_base"
SERVICE_GUIDE_AREA_API_URL="https://10.206.23.71/gateway3/online-service/chatSessionRecord/getAreaTree"
SERVICE_GUIDE_TIMEOUT=30

# ==================== 监控配置 ====================

# 是否启用健康检查端点 (true/false)
ENABLE_HEALTH_CHECK="true"

# 健康检查端点路径
HEALTH_CHECK_PATH="/health"

# 是否启用指标收集 (true/false)
ENABLE_METRICS="false"

# 指标端点路径
METRICS_PATH="/metrics"

# ==================== 开发配置 ====================

# 是否启用调试模式 (true/false)
DEBUG_MODE="false"

# 是否启用热重载 (true/false)
HOT_RELOAD="false"

# 是否显示详细错误信息 (true/false)
VERBOSE_ERRORS="false"

# ==================== 环境变量 ====================

# Python虚拟环境路径 (相对于脚本目录)
VENV_PATH=".venv"

# Python可执行文件名
PYTHON_EXECUTABLE="python3"

# 主程序文件名
MAIN_SCRIPT="main_modular.py"

# ==================== 示例配置 ====================

# 开发环境示例配置:
# MCP_SERVICES="all"
# MCP_PORT=8000
# MCP_HOST="127.0.0.1"
# MCP_DAEMON="false"
# DEBUG_MODE="true"
# HOT_RELOAD="true"

# 生产环境示例配置:
# MCP_SERVICES="social,policy,service-guide"
# MCP_PORT=8000
# MCP_HOST="0.0.0.0"
# MCP_DAEMON="true"
# LOG_LEVEL="WARNING"
# ENABLE_API_KEY="true"
# API_KEY="your-secure-api-key-here"

# 单服务示例配置:
# MCP_SERVICES="social"
# MCP_PORT=8001
# MCP_HOST="0.0.0.0"
# MCP_DAEMON="true"
