# MCP Service Manager

可配置化的MCP服务管理系统，支持动态创建和管理MCP服务。

## 功能特性

- 🚀 **动态服务创建**: 通过配置后端API自动生成MCP服务
- 🎛️ **Web管理界面**: 简单易用的Web界面管理服务
- 📊 **服务监控**: 实时查看服务状态和运行情况
- 🔧 **灵活配置**: 支持各种HTTP API配置
- 📦 **服务管理**: 启动、停止、删除服务操作

## 项目结构

```
mcp-service-manager/
├── src/
│   └── mcp_service_manager/
│       ├── __init__.py
│       ├── main.py              # 主程序入口
│       ├── config_manager.py    # 配置管理
│       ├── service_generator.py # 动态服务生成器
│       ├── service_manager.py   # 服务管理器
│       ├── api/                 # REST API
│       │   ├── __init__.py
│       │   └── routes.py
│       └── web/                 # 前端界面
│           ├── static/
│           └── templates/
├── configs/                     # 配置文件目录
├── logs/                       # 日志目录
├── pyproject.toml
└── README.md
```

## 🛠️ 环境准备

### 系统要求

- Python 3.10+
- uv (推荐的Python包管理器)

### 安装uv

```bash
# Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用pip安装
pip install uv
```

### 项目安装

```bash
# 1. 进入项目目录
cd mcp-service-manager

# 2. 创建虚拟环境
uv venv

# 3. 激活虚拟环境
# Windows
.venv\Scripts\activate
# macOS/Linux
source .venv/bin/activate

# 4. 安装依赖（使用清华源加速）
uv add "mcp[cli]" --index-url https://pypi.tuna.tsinghua.edu.cn/simple
uv add "fastmcp>=2.10.6" --index-url https://pypi.tuna.tsinghua.edu.cn/simple
```

### 启动服务

```bash
# 启动MCP服务管理器
python -m src.mcp_service_manager.main

# 自定义端口和主机
python -m src.mcp_service_manager.main --host 0.0.0.0 --port 8000 --log-level info
```

### 访问界面

- **Web管理界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/health

## 使用说明

1. **添加新服务**: 在Web界面中配置API信息
2. **启动服务**: 选择端口启动MCP服务
3. **管理服务**: 查看状态、停止或删除服务
4. **测试服务**: 通过MCP客户端连接测试

## 📚 REST API接口详细说明

### 健康检查

```http
GET /api/health
```

**响应示例**:
```json
{
  "status": "healthy",
  "message": "MCP服务管理器运行正常"
}
```

### 创建服务

```http
POST /api/services
Content-Type: application/json
```

**请求体**:
```json
{
  "name": "服务名称",
  "description": "服务描述",
  "api_config": {
    "url": "http://api.example.com/endpoint",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer your-token"
    },
    "params": {
      "default_param": "value"
    },
    "timeout": 30
  },
  "port": 8001
}
```

**响应示例**:
```json
{
  "success": true,
  "service_id": "uuid-string",
  "message": "服务创建成功"
}
```

### 获取服务列表

```http
GET /api/services
```

**响应示例**:
```json
{
  "success": true,
  "total": 2,
  "services": [
    {
      "service_id": "uuid-1",
      "name": "服务1",
      "description": "描述1",
      "status": "running",
      "port": 8001,
      "api_url": "http://api.example.com/endpoint1",
      "created_at": "2025-07-29T10:00:00",
      "updated_at": "2025-07-29T10:30:00"
    }
  ]
}
```

### 获取服务详情

```http
GET /api/services/{service_id}
```

**响应示例**:
```json
{
  "success": true,
  "service": {
    "service_id": "uuid-string",
    "name": "服务名称",
    "description": "服务描述",
    "status": "stopped",
    "port": 8001,
    "api_config": {
      "url": "http://api.example.com/endpoint",
      "method": "POST",
      "headers": {"Content-Type": "application/json"},
      "params": {"param": "value"},
      "timeout": 30
    },
    "created_at": "2025-07-29T10:00:00",
    "updated_at": "2025-07-29T10:30:00",
    "runtime_info": null
  }
}
```

### 启动服务

```http
POST /api/services/{service_id}/start
```

**响应示例**:
```json
{
  "success": true,
  "message": "服务启动成功",
  "port": 8001
}
```

### 停止服务

```http
POST /api/services/{service_id}/stop
```

**响应示例**:
```json
{
  "success": true,
  "message": "服务停止成功"
}
```

### 删除服务

```http
DELETE /api/services/{service_id}
```

**响应示例**:
```json
{
  "success": true,
  "message": "服务删除成功"
}
```

### 更新服务配置

```http
PUT /api/services/{service_id}
Content-Type: application/json
```

**请求体**:
```json
{
  "name": "新服务名称",
  "description": "新描述",
  "api_config": {
    "url": "http://new-api.example.com/endpoint",
    "method": "GET",
    "headers": {"Authorization": "Bearer new-token"},
    "params": {"new_param": "new_value"},
    "timeout": 60
  }
}
```

## 🔧 API配置示例

### 示例1：简单GET请求

```json
{
  "name": "天气查询服务",
  "description": "查询城市天气信息",
  "api_config": {
    "url": "http://api.weather.com/v1/current",
    "method": "GET",
    "headers": {
      "API-Key": "your-api-key"
    },
    "params": {
      "units": "metric"
    },
    "timeout": 30
  }
}
```

### 示例2：POST请求（社保查询）

```json
{
  "name": "社保证明查询",
  "description": "查询个人社保证明",
  "api_config": {
    "url": "https://************/gateway3/online-service/chatApi/socialCertificate",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json"
    },
    "params": {},
    "timeout": 30
  }
}
```

### 示例3：政策查询服务

```json
{
  "name": "四川省政策查询",
  "description": "查询四川省相关政策信息",
  "api_config": {
    "url": "http://*************/v1/workflows/run",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json"
    },
    "params": {
      "response_mode": "blocking"
    },
    "timeout": 60
  }
}
```

## 🧪 测试

### 运行API测试

项目包含完整的API测试脚本：

```bash
# 确保服务已启动
python -m src.mcp_service_manager.main &

# 运行测试
python test_api.py
```

测试覆盖：
- ✅ 健康检查
- ✅ 服务创建
- ✅ 服务列表获取
- ✅ 服务详情查询
- ✅ 服务启动/停止
- ✅ 服务删除

### 手动测试

```bash
# 1. 创建测试服务
curl -X POST http://localhost:8000/api/services \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试服务",
    "description": "测试用API服务",
    "api_config": {
      "url": "https://httpbin.org/post",
      "method": "POST",
      "headers": {"Content-Type": "application/json"},
      "params": {"test": true},
      "timeout": 30
    }
  }'

# 2. 启动服务
curl -X POST http://localhost:8000/api/services/{service_id}/start

# 3. 查看服务状态
curl http://localhost:8000/api/services
```

## 🚨 故障排除

### 常见问题

**1. 服务启动失败**
```
错误: 端口被占用
解决: 检查端口使用情况，或让系统自动分配端口
```

**2. API调用失败**
```
错误: 连接超时
解决: 检查API URL是否正确，网络是否可达
```

**3. 虚拟环境问题**
```
错误: 模块未找到
解决: 确保已激活虚拟环境并安装所有依赖
```

**4. 权限问题**
```
错误: 无法创建文件
解决: 检查configs/和logs/目录的写入权限
```

### 日志查看

```bash
# 查看主服务日志
tail -f logs/mcp_service_manager.log

# 查看特定服务日志
tail -f logs/service_{service_id}.log
```

### 调试模式

```bash
# 启动调试模式
python -m src.mcp_service_manager.main --log-level debug --reload
```

## 📈 性能优化

### 建议配置

- **并发服务数**: 建议不超过20个同时运行的服务
- **端口范围**: 默认8001-9000，可根据需要调整
- **超时设置**: 根据后端API响应时间合理设置timeout
- **内存使用**: 每个服务约占用10-20MB内存

### 监控

```bash
# 查看服务状态
curl http://localhost:8000/api/services | jq '.services[] | {name, status, port}'

# 检查端口使用
netstat -an | grep :800
```

## 🔮 扩展功能

### 计划中的功能

- [ ] 服务模板管理
- [ ] 批量服务操作
- [ ] 服务监控和健康检查
- [ ] 配置导入/导出
- [ ] 服务依赖管理
- [ ] 负载均衡支持
- [ ] Docker容器化部署
- [ ] 服务性能统计

### 自定义扩展

系统采用模块化设计，支持以下扩展：

1. **自定义服务模板**: 修改`service_generator.py`
2. **新增API端点**: 扩展`api/routes.py`
3. **前端界面定制**: 修改`web/templates/`和`web/static/`
4. **配置存储后端**: 替换`config_manager.py`中的存储实现

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 开发指南

1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交Pull Request

---

**项目状态**: ✅ 生产就绪
**版本**: v1.0.0
**最后更新**: 2025-07-29
