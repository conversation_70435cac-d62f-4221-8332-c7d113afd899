
import requests
import json
import logging
from fastmcp import FastMCP
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 创建MCP服务
app = FastMCP(name="四川省政策查询服务")

@app.tool()
def api_query(query: str = "", area_code: str = "5100000") -> Dict[str, Any]:
    """
    查询四川省相关政策，在询问政策相关问题时调用。参数：query(用户问题)，area_code(地区编码，默认为四川省：5100000)
    """
    
    # API配置
    url = "http://*************/v1/workflows/run"
    method = "POST"
    headers = {'Authorization': 'Bearer app-SPJtpNohiogcpYBdI6pkt5CJ', 'Content-Type': 'application/json'}
    base_params = {'inputs': {'query': '', 'area_code': '5100000'}, 'response_mode': 'blocking', 'user': 'abc-123'}
    timeout = 30
    
    try:
        # 智能合并基础参数和传入参数
        request_params = base_params.copy()

        # 构建传入参数字典
        input_params = {"query": query, "area_code": area_code}

        # 如果基础参数中有inputs字段，则将参数合并到inputs中
        if "inputs" in request_params and isinstance(request_params["inputs"], dict):
            request_params["inputs"].update(input_params)
        else:
            # 否则直接合并到顶层
            request_params.update(input_params)

        logger.info(f"调用API: {url}")
        logger.info(f"请求参数: {request_params}")
        
        # 根据HTTP方法发送请求
        if method.upper() == "GET":
            response = requests.get(
                url, 
                params=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False  # 注意：生产环境中应该验证SSL证书
            )
        elif method.upper() == "POST":
            response = requests.post(
                url, 
                json=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False
            )
        elif method.upper() == "PUT":
            response = requests.put(
                url, 
                json=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False
            )
        elif method.upper() == "DELETE":
            response = requests.delete(
                url, 
                json=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False
            )
        else:
            return {"error": f"不支持的HTTP方法: {method}"}
        
        # 检查响应状态
        if response.status_code == 200:
            try:
                result = response.json()
                logger.info("API调用成功")
                return result
            except json.JSONDecodeError:
                # 如果响应不是JSON格式，返回文本内容
                return {"content": response.text, "content_type": "text"}
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            return {
                "error": f"API请求失败，状态码: {response.status_code}",
                "status_code": response.status_code,
                "response": response.text
            }
    
    except requests.exceptions.Timeout:
        logger.error("API请求超时")
        return {"error": "API请求超时"}
    except requests.exceptions.ConnectionError:
        logger.error("API连接失败")
        return {"error": "API连接失败"}
    except Exception as e:
        logger.error(f"请求过程中发生错误: {str(e)}")
        return {"error": f"请求过程中发生错误: {str(e)}"}

@app.tool()
def get_service_info() -> Dict[str, Any]:
    """获取服务信息"""
    return {
        "service_id": "28930cb9-7ac5-48cf-bc5c-b1a168baecda",
        "name": "四川省政策查询服务",
        "description": "查询四川省相关政策信息的MCP服务",
        "api_url": "http://*************/v1/workflows/run",
        "method": "POST",
        "port": 15235,
        "status": "running"
    }

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="四川省政策查询服务")
    parser.add_argument("--port", type=int, default=15235, help="服务端口")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")

    args = parser.parse_args()

    print(f"启动MCP服务: 四川省政策查询服务")
    print(f"地址: http://{args.host}:{args.port}")
    print(f"MCP端点: http://{args.host}:{args.port}/mcp")
    print(f"API URL: http://*************/v1/workflows/run")

    # 使用streamable-http传输，这是MCP客户端期望的协议
    app.run(transport="http", host=args.host, port=args.port)
