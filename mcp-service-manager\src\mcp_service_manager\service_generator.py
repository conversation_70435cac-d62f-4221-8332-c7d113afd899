import asyncio
import subprocess
import sys
import os
import tempfile
from typing import Dict, Any, Optional
import logging
from .config_manager import MCPServiceConfig

logger = logging.getLogger(__name__)

class ServiceGenerator:
    """动态MCP服务生成器"""
    
    def __init__(self):
        self.running_processes: Dict[str, subprocess.Popen] = {}

    def _generate_function_params(self, config: MCPServiceConfig) -> str:
        """根据API配置生成函数参数"""
        params = []

        # 从默认参数中提取可配置的参数
        if hasattr(config.api_config, 'params') and config.api_config.params:
            # 如果有inputs嵌套结构
            if 'inputs' in config.api_config.params and isinstance(config.api_config.params['inputs'], dict):
                for key, default_value in config.api_config.params['inputs'].items():
                    if isinstance(default_value, str):
                        params.append(f'{key}: str = "{default_value}"')
                    elif isinstance(default_value, int):
                        params.append(f'{key}: int = {default_value}')
                    elif isinstance(default_value, float):
                        params.append(f'{key}: float = {default_value}')
                    elif isinstance(default_value, bool):
                        params.append(f'{key}: bool = {default_value}')
                    else:
                        params.append(f'{key}: str = "{default_value}"')
            else:
                # 直接从顶层参数提取
                for key, default_value in config.api_config.params.items():
                    if key in ['response_mode', 'user']:  # 跳过固定参数
                        continue
                    if isinstance(default_value, str):
                        params.append(f'{key}: str = "{default_value}"')
                    elif isinstance(default_value, int):
                        params.append(f'{key}: int = {default_value}')
                    elif isinstance(default_value, float):
                        params.append(f'{key}: float = {default_value}')
                    elif isinstance(default_value, bool):
                        params.append(f'{key}: bool = {default_value}')
                    else:
                        params.append(f'{key}: str = "{default_value}"')

        # 如果没有找到参数，提供一个通用的query参数
        if not params:
            params.append('query: str = ""')

        return ', '.join(params)

    def _generate_input_params_code(self, config: MCPServiceConfig) -> str:
        """生成输入参数构建代码"""
        params = []

        # 从默认参数中提取可配置的参数
        if hasattr(config.api_config, 'params') and config.api_config.params:
            # 如果有inputs嵌套结构
            if 'inputs' in config.api_config.params and isinstance(config.api_config.params['inputs'], dict):
                for key in config.api_config.params['inputs'].keys():
                    params.append(f'"{key}": {key}')
            else:
                # 直接从顶层参数提取
                for key in config.api_config.params.keys():
                    if key not in ['response_mode', 'user']:  # 跳过固定参数
                        params.append(f'"{key}": {key}')

        # 如果没有找到参数，提供一个通用的query参数
        if not params:
            params.append('"query": query')

        return f"input_params = {{{', '.join(params)}}}"

    def generate_service_code(self, config: MCPServiceConfig) -> str:
        """根据配置生成MCP服务代码"""
        
        # 生成工具函数名称（安全的函数名）
        tool_name = self._sanitize_function_name(config.name)
        
        service_code = f'''
import requests
import json
import logging
from fastmcp import FastMCP
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 创建MCP服务
app = FastMCP(name="{config.name}")

@app.tool()
def api_query({self._generate_function_params(config)}) -> Dict[str, Any]:
    """
    {config.tool_description if config.tool_description else config.description}
    """
    
    # API配置
    url = "{config.api_config.url}"
    method = "{config.api_config.method}"
    headers = {config.api_config.headers}
    base_params = {config.api_config.params}
    timeout = {config.api_config.timeout}
    
    try:
        # 智能合并基础参数和传入参数
        request_params = base_params.copy()

        # 构建传入参数字典
        {self._generate_input_params_code(config)}

        # 如果基础参数中有inputs字段，则将参数合并到inputs中
        if "inputs" in request_params and isinstance(request_params["inputs"], dict):
            request_params["inputs"].update(input_params)
        else:
            # 否则直接合并到顶层
            request_params.update(input_params)

        logger.info(f"调用API: {{url}}")
        logger.info(f"请求参数: {{request_params}}")
        
        # 根据HTTP方法发送请求
        if method.upper() == "GET":
            response = requests.get(
                url, 
                params=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False  # 注意：生产环境中应该验证SSL证书
            )
        elif method.upper() == "POST":
            response = requests.post(
                url, 
                json=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False
            )
        elif method.upper() == "PUT":
            response = requests.put(
                url, 
                json=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False
            )
        elif method.upper() == "DELETE":
            response = requests.delete(
                url, 
                json=request_params, 
                headers=headers, 
                timeout=timeout,
                verify=False
            )
        else:
            return {{"error": f"不支持的HTTP方法: {{method}}"}}
        
        # 检查响应状态
        if response.status_code == 200:
            try:
                result = response.json()
                logger.info("API调用成功")
                return result
            except json.JSONDecodeError:
                # 如果响应不是JSON格式，返回文本内容
                return {{"content": response.text, "content_type": "text"}}
        else:
            logger.error(f"API请求失败，状态码: {{response.status_code}}")
            return {{
                "error": f"API请求失败，状态码: {{response.status_code}}",
                "status_code": response.status_code,
                "response": response.text
            }}
    
    except requests.exceptions.Timeout:
        logger.error("API请求超时")
        return {{"error": "API请求超时"}}
    except requests.exceptions.ConnectionError:
        logger.error("API连接失败")
        return {{"error": "API连接失败"}}
    except Exception as e:
        logger.error(f"请求过程中发生错误: {{str(e)}}")
        return {{"error": f"请求过程中发生错误: {{str(e)}}"}}

@app.tool()
def get_service_info() -> Dict[str, Any]:
    """获取服务信息"""
    return {{
        "service_id": "{config.service_id}",
        "name": "{config.name}",
        "description": "{config.description}",
        "api_url": "{config.api_config.url}",
        "method": "{config.api_config.method}",
        "port": {config.port},
        "status": "running"
    }}

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="{config.name}")
    parser.add_argument("--port", type=int, default={config.port}, help="服务端口")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")

    args = parser.parse_args()

    print(f"启动MCP服务: {config.name}")
    print(f"地址: http://{{args.host}}:{{args.port}}")
    print(f"MCP端点: http://{{args.host}}:{{args.port}}/mcp")
    print(f"API URL: {config.api_config.url}")

    # 使用streamable-http传输，这是MCP客户端期望的协议
    app.run(transport="http", host=args.host, port=args.port)
'''
        return service_code
    
    def _sanitize_function_name(self, name: str) -> str:
        """将服务名称转换为安全的函数名"""
        # 移除特殊字符，只保留字母、数字和下划线
        import re
        sanitized = re.sub(r'[^a-zA-Z0-9_\u4e00-\u9fff]', '_', name)
        # 确保以字母或下划线开头
        if sanitized and not (sanitized[0].isalpha() or sanitized[0] == '_'):
            sanitized = f"service_{sanitized}"
        # 如果为空，使用默认名称
        if not sanitized:
            sanitized = "api_service"
        return sanitized
    
    def create_service_file(self, config: MCPServiceConfig) -> str:
        """创建服务文件并返回文件路径"""
        service_code = self.generate_service_code(config)
        
        # 创建临时文件
        service_dir = os.path.join("configs", "services")
        os.makedirs(service_dir, exist_ok=True)
        
        service_file = os.path.join(service_dir, f"{config.service_id}.py")
        
        with open(service_file, 'w', encoding='utf-8') as f:
            f.write(service_code)
        
        logger.info(f"已创建服务文件: {service_file}")
        return service_file
    
    def start_service(self, config: MCPServiceConfig) -> bool:
        """启动MCP服务"""
        try:
            # 创建服务文件
            service_file = self.create_service_file(config)
            
            # 启动服务进程 - 使用当前虚拟环境的Python
            python_executable = sys.executable

            # 如果在虚拟环境中，确保使用虚拟环境的Python
            if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
                # 在虚拟环境中
                venv_python = os.path.join(sys.prefix, 'Scripts', 'python.exe') if os.name == 'nt' else os.path.join(sys.prefix, 'bin', 'python')
                if os.path.exists(venv_python):
                    python_executable = venv_python

            cmd = [python_executable, service_file, "--port", str(config.port)]
            
            logger.info(f"启动服务命令: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )
            
            # 存储进程引用
            self.running_processes[config.service_id] = process
            
            logger.info(f"服务已启动: {config.name} (PID: {process.pid}, Port: {config.port})")
            return True
            
        except Exception as e:
            logger.error(f"启动服务失败: {e}")
            return False
    
    def stop_service(self, service_id: str) -> bool:
        """停止MCP服务"""
        try:
            if service_id in self.running_processes:
                process = self.running_processes[service_id]
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # 如果进程没有在5秒内结束，强制杀死
                    process.kill()
                    process.wait()
                
                del self.running_processes[service_id]
                logger.info(f"服务已停止: {service_id}")
                return True
            else:
                logger.warning(f"服务未运行: {service_id}")
                return False
                
        except Exception as e:
            logger.error(f"停止服务失败: {e}")
            return False
    
    def is_service_running(self, service_id: str) -> bool:
        """检查服务是否正在运行"""
        if service_id not in self.running_processes:
            return False
        
        process = self.running_processes[service_id]
        return process.poll() is None
    
    def get_service_status(self, service_id: str) -> Dict[str, Any]:
        """获取服务状态"""
        if service_id in self.running_processes:
            process = self.running_processes[service_id]
            is_running = process.poll() is None
            return {
                "service_id": service_id,
                "running": is_running,
                "pid": process.pid if is_running else None
            }
        else:
            return {
                "service_id": service_id,
                "running": False,
                "pid": None
            }
    
    def cleanup_service_file(self, service_id: str):
        """清理服务文件"""
        service_file = os.path.join("configs", "services", f"{service_id}.py")
        try:
            if os.path.exists(service_file):
                os.remove(service_file)
                logger.info(f"已删除服务文件: {service_file}")
        except Exception as e:
            logger.error(f"删除服务文件失败: {e}")
    
    def stop_all_services(self):
        """停止所有服务"""
        for service_id in list(self.running_processes.keys()):
            self.stop_service(service_id)
