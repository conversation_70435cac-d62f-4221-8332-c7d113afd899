#!/bin/bash

# MCP服务器启动脚本
# 用法: ./start_mcp_server.sh [选项]
# 选项:
#   --services <service1,service2>  指定要启动的服务 (默认: all)
#   --port <port>                   指定端口 (默认: 8000)
#   --host <host>                   指定主机地址 (默认: 0.0.0.0)
#   --daemon                        后台运行模式
#   --config <config_file>          指定配置文件
#   --help                          显示帮助信息

set -e  # 遇到错误时退出

# 默认配置
DEFAULT_SERVICES="all"
DEFAULT_PORT=8000
DEFAULT_HOST="0.0.0.0"
DEFAULT_DAEMON=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/mcp_server.pid"
LOG_FILE="$SCRIPT_DIR/mcp_server.log"
CONFIG_FILE="$SCRIPT_DIR/server_config.conf"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
MCP服务器启动脚本

用法: $0 [选项]

选项:
    --services <services>    指定要启动的服务，用逗号分隔
                            可选值: social,policy,service-guide,all
                            默认: all
    --port <port>           指定服务端口 (默认: 8000)
    --host <host>           指定主机地址 (默认: 0.0.0.0)
    --daemon                后台运行模式
    --config <config_file>  指定配置文件路径
    --help                  显示此帮助信息

示例:
    $0                                          # 启动所有服务，使用默认配置
    $0 --services social,policy --port 9000    # 启动指定服务，使用端口9000
    $0 --daemon                                 # 后台运行所有服务
    $0 --config /path/to/config.conf           # 使用指定配置文件

EOF
}

# 加载配置文件
load_config() {
    if [[ -f "$1" ]]; then
        log_info "加载配置文件: $1"
        source "$1"
    else
        log_warn "配置文件不存在: $1，使用默认配置"
    fi
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装或不在PATH中"
        exit 1
    fi
    
    local python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_info "Python版本: $python_version"
    
    # 检查虚拟环境
    if [[ ! -d "$SCRIPT_DIR/.venv" ]]; then
        log_error "虚拟环境不存在: $SCRIPT_DIR/.venv"
        log_error "请先运行: uv venv"
        exit 1
    fi
    
    # 检查主程序文件
    if [[ ! -f "$SCRIPT_DIR/main_modular.py" ]]; then
        log_error "主程序文件不存在: $SCRIPT_DIR/main_modular.py"
        exit 1
    fi
    
    log_info "环境检查通过"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":$port "; then
            log_error "端口 $port 已被占用"
            log_info "请使用其他端口或停止占用该端口的进程"
            exit 1
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln | grep -q ":$port "; then
            log_error "端口 $port 已被占用"
            log_info "请使用其他端口或停止占用该端口的进程"
            exit 1
        fi
    else
        log_warn "无法检查端口占用情况 (netstat 和 ss 命令都不可用)"
    fi
}

# 检查是否已经运行
check_running() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_error "MCP服务器已在运行 (PID: $pid)"
            log_info "如需重启，请先运行停止脚本: ./stop_mcp_server.sh"
            exit 1
        else
            log_warn "发现过期的PID文件，正在清理..."
            rm -f "$PID_FILE"
        fi
    fi
}

# 激活虚拟环境
activate_venv() {
    log_info "激活虚拟环境..."
    
    local venv_activate="$SCRIPT_DIR/.venv/bin/activate"
    if [[ ! -f "$venv_activate" ]]; then
        log_error "虚拟环境激活脚本不存在: $venv_activate"
        exit 1
    fi
    
    source "$venv_activate"
    log_info "虚拟环境已激活"
}

# 启动服务器
start_server() {
    local services="$1"
    local port="$2"
    local host="$3"
    local daemon="$4"
    
    log_info "启动MCP服务器..."
    log_info "服务: $services"
    log_info "地址: http://$host:$port"
    log_info "后台运行: $daemon"
    
    cd "$SCRIPT_DIR"
    
    # 构建命令
    local cmd="python3 main_modular.py --port $port --host $host"
    
    if [[ "$services" != "all" ]]; then
        # 将逗号分隔的服务转换为空格分隔
        local services_args=$(echo "$services" | tr ',' ' ')
        cmd="$cmd --services $services_args"
    fi
    
    if [[ "$daemon" == "true" ]]; then
        # 后台运行
        log_info "以后台模式启动服务器..."
        nohup $cmd > "$LOG_FILE" 2>&1 &
        local pid=$!
        echo $pid > "$PID_FILE"
        
        # 等待一下确保服务启动
        sleep 2
        
        if kill -0 "$pid" 2>/dev/null; then
            log_info "服务器已启动 (PID: $pid)"
            log_info "日志文件: $LOG_FILE"
            log_info "PID文件: $PID_FILE"
        else
            log_error "服务器启动失败"
            rm -f "$PID_FILE"
            exit 1
        fi
    else
        # 前台运行
        log_info "以前台模式启动服务器..."
        exec $cmd
    fi
}

# 主函数
main() {
    local services="$DEFAULT_SERVICES"
    local port="$DEFAULT_PORT"
    local host="$DEFAULT_HOST"
    local daemon="$DEFAULT_DAEMON"
    local config_file=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --services)
                services="$2"
                shift 2
                ;;
            --port)
                port="$2"
                shift 2
                ;;
            --host)
                host="$2"
                shift 2
                ;;
            --daemon)
                daemon="true"
                shift
                ;;
            --config)
                config_file="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 加载配置文件
    if [[ -n "$config_file" ]]; then
        load_config "$config_file"
    elif [[ -f "$CONFIG_FILE" ]]; then
        load_config "$CONFIG_FILE"
    fi
    
    # 从配置文件覆盖默认值（如果配置文件中有定义）
    services="${MCP_SERVICES:-$services}"
    port="${MCP_PORT:-$port}"
    host="${MCP_HOST:-$host}"
    daemon="${MCP_DAEMON:-$daemon}"
    
    log_info "开始启动MCP服务器..."
    
    # 执行启动流程
    check_environment
    check_running
    check_port "$port"
    activate_venv
    start_server "$services" "$port" "$host" "$daemon"
}

# 捕获信号，确保清理
trap 'log_warn "收到中断信号，正在清理..."; exit 130' INT TERM

# 运行主函数
main "$@"
